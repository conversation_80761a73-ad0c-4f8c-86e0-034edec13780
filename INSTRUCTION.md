+++markdown

# 📦 Proxmox Extension dengan Tema Materially & Reverse Proxy Management

Proyek ini bertujuan membuat **extension untuk Proxmox** dengan tampilan admin UI menyerupai [Materially - React Admin Template](https://github.com/codedthemes/materially-free-react-admin-template), serta dukungan manajemen reverse proxy menggunakan **NGINX + Certbot** dan **iptables-persistent**, serta API menggunakan **FastAPI**.

---

## 🛠️ Struktur Extension Proxmox

### 1. Server Setting

Form Edit Server:

* **Host**
* **Port**
* **User**
* **Password**
* **Realm**
* **Default Node**
* **Storage**
* **LXC Template Storage**

### 2. Network Setting

* **Public Network Bridge**
* **Public IP Range**
* **Public Subnet**
* **Public Gateway**
* **Private Network Bridge**
* **Private IP Range**
* **Private Subnet**
* **Private Gateway**
* **DNS Server**

### 3. Proxy Setting

* **API URL**
* **Token**

---

## 📦 Product Configuration

### Product Edit Form

* **Select Server**
* **VM Type**: `KVM` / `LXC`
* **CPU**
* **Memory**
* **Disk Size**
* **Bandwidth**
* **Network Type**: `Public` / `Private`

### User Order Form

* **Hostname**
* **Template**
* **Password**
* **Public Key (Optional)**
* **Enable Proxy**
* **SSL**
* **Domain**
* **Port Forwarding**

  * Ex: `22`
  * \[+] Add more

---

## 🧩 UI Theme & Dashboard (Materially-like)

Jika Materially original tidak dapat digunakan, gunakan UI alternatif seperti:

* [MUI + React](https://mui.com/)
* [CoreUI](https://coreui.io/react/)
* [Tabler UI](https://tabler.io/)

### Dashboard Menampilkan:

#### 📌 Product Detail

* **Name**
* **Price**
* **Billing Cycle**
* **Status**

#### 📡 Server Detail

* **VM Status**: `Started / Stopped`
* **IP Address**
* **Password**: Hidden (eye icon to toggle)

#### 🌐 Proxy Information

* **Domain**
* **Proxy IP**
* **SSL**: true / false
* **Port Forwards**

  * Edit Option Available

---

## ⚙️ Actions Menu

* **Start**
* **Stop**
* **Restart**
* **Reinstall**
* **Upgrade**

### 🔁 Reinstall Page

Form input mirip User Order:

* Hostname
* Template
* Password
* Public Key (optional)
* Enable Proxy
* Domain
* Port Forward

> ✅ Pastikan setiap **action** (start, stop, restart) memberikan **notifikasi real-time (toast/snackbar)**.

---

## 🔧 Proxy Management API (FastAPI)

### `/api/proxy`

* **domain**: `string`
* **target**: `ip:port`
* **ssl**: `bool`

### `/api/port-forward`

* **port**: `{22, 21, ...}`
* **target**: `ip:port`
* **response**: `success/error`
* **state**:

  ```json
  [
    {"port": 22, "dest": "********"},
    {"port": 21, "dest": "********"}
  ]
  ```

---

## 🔐 Proxy Setup Script

**proxy-setup.sh**

Installs & configures:

* `nginx`
* `certbot`
* `iptables-persistent`

Contoh isi:

```bash
#!/bin/bash
apt update
apt install -y nginx python3-certbot-nginx iptables-persistent ufw

# Enable basic UFW firewall
ufw allow 'Nginx Full'
ufw enable

# Save current iptables rules
iptables-save > /etc/iptables/rules.v4

echo "Proxy setup completed."
```

---

## 🧬 FastAPI Proxy Server (Basic Example)

```python
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI()

class ProxyConfig(BaseModel):
    domain: str
    target: str
    ssl: bool

class PortForward(BaseModel):
    port: int
    target: str

@app.post("/api/proxy")
def create_proxy(config: ProxyConfig):
    # Implement nginx config creation and certbot here
    return {"status": "success", "domain": config.domain}

@app.post("/api/port-forward")
def forward_port(pf: PortForward):
    # Implement iptables rule append
    return {"status": "success", "port": pf.port, "target": pf.target}
```

---

## 📄 Halaman Edit Proxy

* Form Edit:

  * **Domain**
  * **Target**
  * **SSL**
  * **List/Edit Port Forwards**

---

## 🧪 Catatan Tambahan

* Gunakan **Docker** atau **Supervisor** untuk menjalankan FastAPI secara stabil di production.
* Backend dan frontend dapat berkomunikasi via JWT token di header authorization.

---

> Jika Anda butuh bantuan implementasi kode React-nya (theme layout atau API calls), saya siap bantu lanjut!
> +++
