@extends('layouts.app')

@section('title', __('Console - :hostname', ['hostname' => $server['name'] ?? 'VM-' . $server['vm_id']]))

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">
                        {{ __('Console Access') }}
                    </h1>
                    <p class="text-gray-600 mt-1">
                        {{ __('VM ID: :vmid on Node: :node', ['vmid' => $server['vm_id'], 'node' => $server['node']]) }}
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        {{ $server['status'] === 'running' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        <span class="w-2 h-2 rounded-full mr-2
                            {{ $server['status'] === 'running' ? 'bg-green-400' : 'bg-red-400' }}"></span>
                        {{ ucfirst($server['status'] ?? 'unknown') }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Server Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{{ __('CPU Cores') }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $server['cpus'] ?? 'N/A' }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{{ __('Memory') }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ isset($server['maxmem']) ? round($server['maxmem'] / 1024 / 1024 / 1024, 1) . ' GB' : 'N/A' }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{{ __('Disk') }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ isset($server['maxdisk']) ? round($server['maxdisk'] / 1024 / 1024 / 1024, 1) . ' GB' : 'N/A' }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{{ __('Type') }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ strtoupper($server['vm_type'] ?? 'KVM') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Console Options -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Console Access Options') }}</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Proxmox Web Console -->
                <div class="border rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="font-medium text-gray-900">{{ __('Proxmox Web Console') }}</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">{{ __('Access the VM console directly through Proxmox web interface') }}</p>
                    <a href="{{ $console_url }}" target="_blank" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        {{ __('Open Console') }}
                    </a>
                </div>

                <!-- SSH Access -->
                @if($service->properties()->where('key', 'ip_address')->first())
                <div class="border rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="font-medium text-gray-900">{{ __('SSH Access') }}</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">{{ __('Connect via SSH using your terminal') }}</p>
                    <div class="bg-gray-50 rounded-lg p-3 mb-3">
                        <code class="text-sm text-gray-800">ssh root@{{ $service->properties()->where('key', 'ip_address')->first()->value }}</code>
                    </div>
                    <button onclick="copyToClipboard('ssh root@{{ $service->properties()->where('key', 'ip_address')->first()->value }}')"
                            class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        {{ __('Copy SSH Command') }}
                    </button>
                </div>
                @endif
            </div>
        </div>

        <!-- Connection Information -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Connection Information') }}</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">{{ __('Server Details') }}</h3>
                    <dl class="space-y-2">
                        @if($service->properties()->where('key', 'hostname')->first())
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-600">{{ __('Hostname') }}:</dt>
                            <dd class="text-sm font-medium text-gray-900">{{ $service->properties()->where('key', 'hostname')->first()->value }}</dd>
                        </div>
                        @endif
                        
                        @if($service->properties()->where('key', 'ip_address')->first())
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-600">{{ __('IP Address') }}:</dt>
                            <dd class="text-sm font-medium text-gray-900">{{ $service->properties()->where('key', 'ip_address')->first()->value }}</dd>
                        </div>
                        @endif
                        
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-600">{{ __('VM ID') }}:</dt>
                            <dd class="text-sm font-medium text-gray-900">{{ $server['vm_id'] }}</dd>
                        </div>
                        
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-600">{{ __('Node') }}:</dt>
                            <dd class="text-sm font-medium text-gray-900">{{ $server['node'] }}</dd>
                        </div>
                    </dl>
                </div>

                <div>
                    <h3 class="font-medium text-gray-900 mb-2">{{ __('Access Notes') }}</h3>
                    <div class="text-sm text-gray-600 space-y-2">
                        <p>• {{ __('Use the Proxmox web console for direct VM access') }}</p>
                        <p>• {{ __('SSH is available once the VM is fully booted') }}</p>
                        <p>• {{ __('Default username is usually "root"') }}</p>
                        @if($service->properties()->where('key', 'ssh_key')->first())
                        <p>• {{ __('SSH key authentication is configured') }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        alert('{{ __("Copied to clipboard!") }}');
    }, function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>
@endsection
