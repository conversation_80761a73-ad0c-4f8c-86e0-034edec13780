@extends('layouts.app')

@section('title', __('Reinstall Server'))

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">
                        {{ __('Reinstall Server') }}
                    </h1>
                    <p class="text-gray-600 mt-1">
                        {{ __('Service: :name', ['name' => $service->product->name]) }}
                    </p>
                </div>
                <div class="flex items-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        {{ __('Destructive Action') }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Warning Notice -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-red-800 mb-2">{{ __('Important Warning') }}</h3>
                    <div class="text-red-700 space-y-1">
                        <p>• {{ __('This action will completely wipe your server and reinstall the operating system') }}</p>
                        <p>• {{ __('All data, files, and configurations will be permanently lost') }}</p>
                        <p>• {{ __('This action cannot be undone') }}</p>
                        <p>• {{ __('Make sure you have backed up any important data before proceeding') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Server Information -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Current Server Information') }}</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <dl class="space-y-3">
                        @if($service->properties()->where('key', 'hostname')->first())
                        <div>
                            <dt class="text-sm font-medium text-gray-600">{{ __('Hostname') }}</dt>
                            <dd class="text-sm text-gray-900">{{ $service->properties()->where('key', 'hostname')->first()->value }}</dd>
                        </div>
                        @endif
                        
                        @if($service->properties()->where('key', 'template')->first())
                        <div>
                            <dt class="text-sm font-medium text-gray-600">{{ __('Current Template') }}</dt>
                            <dd class="text-sm text-gray-900">{{ $service->properties()->where('key', 'template')->first()->value }}</dd>
                        </div>
                        @endif
                        
                        @if($service->properties()->where('key', 'vm_type')->first())
                        <div>
                            <dt class="text-sm font-medium text-gray-600">{{ __('VM Type') }}</dt>
                            <dd class="text-sm text-gray-900">{{ strtoupper($service->properties()->where('key', 'vm_type')->first()->value) }}</dd>
                        </div>
                        @endif
                    </dl>
                </div>
                
                <div>
                    <dl class="space-y-3">
                        @if($service->properties()->where('key', 'ip_address')->first())
                        <div>
                            <dt class="text-sm font-medium text-gray-600">{{ __('IP Address') }}</dt>
                            <dd class="text-sm text-gray-900">{{ $service->properties()->where('key', 'ip_address')->first()->value }}</dd>
                        </div>
                        @endif
                        
                        @if($service->properties()->where('key', 'vm_id')->first())
                        <div>
                            <dt class="text-sm font-medium text-gray-600">{{ __('VM ID') }}</dt>
                            <dd class="text-sm text-gray-900">{{ $service->properties()->where('key', 'vm_id')->first()->value }}</dd>
                        </div>
                        @endif
                        
                        @if($service->properties()->where('key', 'node')->first())
                        <div>
                            <dt class="text-sm font-medium text-gray-600">{{ __('Node') }}</dt>
                            <dd class="text-sm text-gray-900">{{ $service->properties()->where('key', 'node')->first()->value }}</dd>
                        </div>
                        @endif
                    </dl>
                </div>
            </div>
        </div>

        <!-- Reinstall Form -->
        <form method="POST" action="{{ route('services.action', ['service' => $service->id, 'action' => 'reinstall']) }}" 
              onsubmit="return confirmReinstall()" class="bg-white rounded-lg shadow-sm border p-6">
            @csrf
            
            <h2 class="text-lg font-semibold text-gray-900 mb-6">{{ __('Reinstall Configuration') }}</h2>
            
            <div class="space-y-6">
                <!-- Template Selection -->
                <div>
                    <label for="template" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('Operating System Template') }}
                    </label>
                    <select name="template" id="template" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{{ __('Select a template...') }}</option>
                        @foreach($templates as $key => $name)
                            <option value="{{ $key }}" 
                                    {{ $key === $service->properties()->where('key', 'template')->first()?->value ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                    <p class="text-sm text-gray-600 mt-1">{{ __('Choose the operating system to install') }}</p>
                </div>

                <!-- Root Password -->
                <div>
                    <label for="root_password" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('New Root Password') }}
                    </label>
                    <input type="password" name="root_password" id="root_password" required minlength="8"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="{{ __('Enter new root password') }}">
                    <p class="text-sm text-gray-600 mt-1">{{ __('Minimum 8 characters required') }}</p>
                </div>

                <!-- SSH Key (Optional) -->
                <div>
                    <label for="ssh_key" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('SSH Public Key (Optional)') }}
                    </label>
                    <textarea name="ssh_key" id="ssh_key" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="{{ __('Paste your SSH public key here for key-based authentication') }}">{{ $service->properties()->where('key', 'ssh_key')->first()?->value }}</textarea>
                    <p class="text-sm text-gray-600 mt-1">{{ __('Optional: Add SSH key for secure access') }}</p>
                </div>

                <!-- Confirmation Checkbox -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input type="checkbox" name="confirm_reinstall" id="confirm_reinstall" required
                                   class="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500">
                        </div>
                        <div class="ml-3">
                            <label for="confirm_reinstall" class="text-sm font-medium text-gray-900">
                                {{ __('I understand that this action will permanently delete all data') }}
                            </label>
                            <p class="text-sm text-gray-600">
                                {{ __('Check this box to confirm that you understand the consequences of reinstalling') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 mt-6 border-t border-gray-200">
                <a href="{{ route('services.show', $service->id) }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('Cancel') }}
                </a>
                
                <button type="submit" 
                        class="inline-flex items-center px-6 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    {{ __('Reinstall Server') }}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function confirmReinstall() {
    const template = document.getElementById('template').value;
    const confirmed = document.getElementById('confirm_reinstall').checked;
    
    if (!template) {
        alert('{{ __("Please select an operating system template") }}');
        return false;
    }
    
    if (!confirmed) {
        alert('{{ __("Please confirm that you understand this action will delete all data") }}');
        return false;
    }
    
    return confirm('{{ __("Are you absolutely sure you want to reinstall this server? This action cannot be undone and will permanently delete all data.") }}');
}
</script>
@endsection
