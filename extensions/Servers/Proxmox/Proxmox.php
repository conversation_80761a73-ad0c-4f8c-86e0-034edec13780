<?php

namespace Paymenter\Extensions\Servers\Proxmox;

use App\Classes\Extension\Server;
use App\Models\Service;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\HtmlString;

/**
 * Class Proxmox
 * Proxmox VE Server Extension for Paymenter
 */
class Proxmox extends Server
{
    /**
     * Get extension configuration
     */
    public function getConfig($values = []): array
    {
        return [
            // Server Settings
            [
                'name' => 'host',
                'label' => 'Proxmox Host',
                'type' => 'text',
                'default' => 'https://proxmox.example.com:8006',
                'description' => 'Proxmox VE host URL with port',
                'required' => true,
                'validation' => 'url',
            ],
            [
                'name' => 'port',
                'label' => 'Port',
                'type' => 'number',
                'default' => 8006,
                'description' => 'Proxmox VE API port',
                'required' => true,
            ],
            [
                'name' => 'user',
                'label' => 'Username',
                'type' => 'text',
                'default' => 'root@pam',
                'description' => 'Proxmox VE username with realm',
                'required' => true,
            ],
            [
                'name' => 'password',
                'label' => 'Password',
                'type' => 'password',
                'description' => 'Proxmox VE password',
                'required' => true,
                'encrypted' => true,
            ],
            [
                'name' => 'realm',
                'label' => 'Realm',
                'type' => 'select',
                'default' => 'pam',
                'description' => 'Authentication realm',
                'required' => true,
                'options' => [
                    'pam' => 'PAM',
                    'pve' => 'PVE',
                    'ad' => 'Active Directory',
                    'ldap' => 'LDAP',
                ],
            ],
            [
                'name' => 'default_node',
                'label' => 'Default Node',
                'type' => 'text',
                'description' => 'Default Proxmox node name',
                'required' => true,
            ],
            [
                'name' => 'storage',
                'label' => 'Storage',
                'type' => 'text',
                'default' => 'local-lvm',
                'description' => 'Default storage for VM disks',
                'required' => true,
            ],
            [
                'name' => 'lxc_template_storage',
                'label' => 'LXC Template Storage',
                'type' => 'text',
                'default' => 'local',
                'description' => 'Storage for LXC templates',
                'required' => true,
            ],
            
            // Network Settings
            [
                'name' => 'public_bridge',
                'label' => 'Public Network Bridge',
                'type' => 'text',
                'default' => 'vmbr0',
                'description' => 'Bridge for public network',
                'required' => true,
            ],
            [
                'name' => 'public_ip_range',
                'label' => 'Public IP Range',
                'type' => 'text',
                'description' => 'Public IP range (e.g., ***********00-*************)',
                'required' => false,
            ],
            [
                'name' => 'public_subnet',
                'label' => 'Public Subnet',
                'type' => 'text',
                'default' => '***********/24',
                'description' => 'Public subnet CIDR',
                'required' => true,
            ],
            [
                'name' => 'public_gateway',
                'label' => 'Public Gateway',
                'type' => 'text',
                'default' => '***********',
                'description' => 'Public network gateway',
                'required' => true,
            ],
            [
                'name' => 'private_bridge',
                'label' => 'Private Network Bridge',
                'type' => 'text',
                'default' => 'vmbr1',
                'description' => 'Bridge for private network',
                'required' => false,
            ],
            [
                'name' => 'private_ip_range',
                'label' => 'Private IP Range',
                'type' => 'text',
                'description' => 'Private IP range (e.g., ********00-**********)',
                'required' => false,
            ],
            [
                'name' => 'private_subnet',
                'label' => 'Private Subnet',
                'type' => 'text',
                'default' => '10.0.0.0/24',
                'description' => 'Private subnet CIDR',
                'required' => false,
            ],
            [
                'name' => 'private_gateway',
                'label' => 'Private Gateway',
                'type' => 'text',
                'default' => '********',
                'description' => 'Private network gateway',
                'required' => false,
            ],
            [
                'name' => 'dns_server',
                'label' => 'DNS Server',
                'type' => 'text',
                'default' => '*******',
                'description' => 'DNS server for VMs',
                'required' => true,
            ],
            
            // Proxy Settings
            [
                'name' => 'proxy_api_url',
                'label' => 'Proxy API URL',
                'type' => 'text',
                'description' => 'FastAPI proxy management URL',
                'required' => false,
                'validation' => 'url',
            ],
            [
                'name' => 'proxy_token',
                'label' => 'Proxy API Token',
                'type' => 'password',
                'description' => 'Authentication token for proxy API',
                'required' => false,
                'encrypted' => true,
            ],
        ];
    }

    /**
     * Test configuration
     */
    public function testConfig(): bool|string
    {
        try {
            $this->authenticate();
            $this->request('/api2/json/version', 'GET');
        } catch (\Exception $e) {
            return $e->getMessage();
        }

        return true;
    }

    /**
     * Authenticate with Proxmox API
     */
    private function authenticate(): string
    {
        $response = Http::post(rtrim($this->config('host'), '/') . '/api2/json/access/ticket', [
            'username' => $this->config('user'),
            'password' => $this->config('password'),
        ]);

        if (!$response->successful()) {
            throw new \Exception('Authentication failed: ' . $response->body());
        }

        $data = $response->json();
        return $data['data']['ticket'] ?? '';
    }

    /**
     * Make API request to Proxmox
     */
    public function request($url, $method = 'GET', $data = []): array
    {
        $ticket = $this->authenticate();
        $req_url = rtrim($this->config('host'), '/') . $url;
        
        $response = Http::withHeaders([
            'Cookie' => 'PVEAuthCookie=' . $ticket,
            'CSRFPreventionToken' => $ticket,
        ])->$method($req_url, $data);

        if (!$response->successful()) {
            throw new \Exception('API request failed: ' . $response->body());
        }

        return $response->json() ?? [];
    }

    /**
     * Get product configuration for admin
     */
    public function getProductConfig($values = []): array
    {
        // Get available nodes
        $nodes = [];
        try {
            $nodeList = $this->request('/api2/json/nodes');
            foreach ($nodeList['data'] as $node) {
                $nodes[$node['node']] = $node['node'];
            }
        } catch (\Exception $e) {
            $nodes[$this->config('default_node')] = $this->config('default_node');
        }

        return [
            [
                'name' => 'server_node',
                'label' => 'Server Node',
                'type' => 'select',
                'description' => 'Proxmox node to deploy on',
                'options' => $nodes,
                'default' => $this->config('default_node'),
                'required' => true,
            ],
            [
                'name' => 'vm_type',
                'label' => 'VM Type',
                'type' => 'select',
                'description' => 'Virtual machine type',
                'options' => [
                    'kvm' => 'KVM (Full Virtualization)',
                    'lxc' => 'LXC (Container)',
                ],
                'default' => 'kvm',
                'required' => true,
            ],
            [
                'name' => 'cpu_cores',
                'label' => 'CPU Cores',
                'type' => 'number',
                'default' => 1,
                'min_value' => 1,
                'max_value' => 32,
                'description' => 'Number of CPU cores',
                'required' => true,
            ],
            [
                'name' => 'memory',
                'label' => 'Memory (MB)',
                'type' => 'number',
                'default' => 1024,
                'min_value' => 512,
                'suffix' => 'MB',
                'description' => 'RAM in megabytes',
                'required' => true,
            ],
            [
                'name' => 'disk_size',
                'label' => 'Disk Size (GB)',
                'type' => 'number',
                'default' => 20,
                'min_value' => 10,
                'suffix' => 'GB',
                'description' => 'Disk size in gigabytes',
                'required' => true,
            ],
            [
                'name' => 'bandwidth',
                'label' => 'Bandwidth Limit (Mbps)',
                'type' => 'number',
                'default' => 100,
                'min_value' => 1,
                'suffix' => 'Mbps',
                'description' => 'Network bandwidth limit',
                'required' => false,
            ],
            [
                'name' => 'network_type',
                'label' => 'Network Type',
                'type' => 'select',
                'description' => 'Network configuration type',
                'options' => [
                    'public' => 'Public Network',
                    'private' => 'Private Network',
                    'both' => 'Both Networks',
                ],
                'default' => 'public',
                'required' => true,
            ],
        ];
    }

    /**
     * Get checkout configuration for users
     */
    public function getCheckoutConfig($product, $values = []): array
    {
        // Get available templates based on VM type
        $templates = [];
        $vmType = $values['vm_type'] ?? 'kvm';

        try {
            if ($vmType === 'lxc') {
                // Get LXC templates
                $templateList = $this->request('/api2/json/nodes/' . $this->config('default_node') . '/aplinfo');
                foreach ($templateList['data'] as $template) {
                    $templates[$template['template']] = $template['headline'] ?? $template['template'];
                }
            } else {
                // For KVM, we'll use ISO images or predefined templates
                $templates = [
                    'ubuntu-20.04' => 'Ubuntu 20.04 LTS',
                    'ubuntu-22.04' => 'Ubuntu 22.04 LTS',
                    'debian-11' => 'Debian 11',
                    'debian-12' => 'Debian 12',
                    'centos-7' => 'CentOS 7',
                    'centos-8' => 'CentOS 8',
                ];
            }
        } catch (\Exception $e) {
            $templates = [
                'ubuntu-22.04' => 'Ubuntu 22.04 LTS',
                'debian-12' => 'Debian 12',
            ];
        }

        return [
            [
                'name' => 'hostname',
                'label' => 'Hostname',
                'type' => 'text',
                'description' => 'Server hostname',
                'required' => true,
                'validation' => 'regex:/^[a-zA-Z0-9\-\.]+$/',
            ],
            [
                'name' => 'template',
                'label' => 'Template/OS',
                'type' => 'select',
                'description' => 'Operating system template',
                'options' => $templates,
                'required' => true,
            ],
            [
                'name' => 'root_password',
                'label' => 'Root Password',
                'type' => 'password',
                'description' => 'Root/Administrator password',
                'required' => true,
                'validation' => 'min:8',
            ],
            [
                'name' => 'ssh_key',
                'label' => 'SSH Public Key (Optional)',
                'type' => 'textarea',
                'description' => 'SSH public key for key-based authentication',
                'required' => false,
            ],
            [
                'name' => 'enable_proxy',
                'label' => 'Enable Reverse Proxy',
                'type' => 'checkbox',
                'description' => 'Enable reverse proxy management',
                'required' => false,
            ],
            [
                'name' => 'ssl_enabled',
                'label' => 'Enable SSL',
                'type' => 'checkbox',
                'description' => 'Enable SSL certificate (requires domain)',
                'required' => false,
            ],
            [
                'name' => 'domain',
                'label' => 'Domain',
                'type' => 'text',
                'description' => 'Domain name for proxy (required if proxy enabled)',
                'required' => false,
                'validation' => 'regex:/^[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}$/',
            ],
            [
                'name' => 'port_forwards',
                'label' => 'Port Forwarding',
                'type' => 'tags',
                'description' => 'Ports to forward (e.g., 22, 80, 443)',
                'required' => false,
                'database_type' => 'array',
            ],
        ];
    }

    /**
     * Create a new server
     */
    public function createServer(Service $service, $settings, $properties)
    {
        // Check if server already exists
        if ($this->getServer($service->id, failIfNotFound: false)) {
            throw new \Exception('Server already exists');
        }

        // Merge settings and properties
        $config = array_merge($settings, $properties);
        $node = $config['server_node'] ?? $this->config('default_node');
        $vmType = $config['vm_type'] ?? 'kvm';

        // Get next available VM ID
        $vmId = $this->getNextVmId($node);

        // Prepare network configuration
        $networkConfig = $this->prepareNetworkConfig($config);

        try {
            if ($vmType === 'lxc') {
                $result = $this->createLxcContainer($service, $config, $vmId, $node, $networkConfig);
            } else {
                $result = $this->createKvmVm($service, $config, $vmId, $node, $networkConfig);
            }

            // Store VM information in service properties
            $service->properties()->updateOrCreate(['key' => 'vm_id'], [
                'name' => 'VM ID',
                'value' => $vmId,
            ]);

            $service->properties()->updateOrCreate(['key' => 'vm_type'], [
                'name' => 'VM Type',
                'value' => $vmType,
            ]);

            $service->properties()->updateOrCreate(['key' => 'node'], [
                'name' => 'Node',
                'value' => $node,
            ]);

            // Store SSH key if provided
            if (!empty($config['ssh_key'])) {
                $service->properties()->updateOrCreate(['key' => 'ssh_key'], [
                    'name' => 'SSH Key',
                    'value' => $config['ssh_key'],
                ]);
            }

            // Store other important properties
            $service->properties()->updateOrCreate(['key' => 'hostname'], [
                'name' => 'Hostname',
                'value' => $config['hostname'] ?? 'vm-' . $service->id,
            ]);

            $service->properties()->updateOrCreate(['key' => 'template'], [
                'name' => 'Template',
                'value' => $config['template'] ?? 'ubuntu-22.04',
            ]);

            if (!empty($result['ip'])) {
                $service->properties()->updateOrCreate(['key' => 'ip_address'], [
                    'name' => 'IP Address',
                    'value' => $result['ip'],
                ]);
            }

            // Setup proxy if enabled
            if ($config['enable_proxy'] && !empty($config['domain'])) {
                $this->setupProxy($service, $config, $result['ip']);
            }

            return [
                'vm_id' => $vmId,
                'ip_address' => $result['ip'],
                'status' => 'created',
            ];

        } catch (\Exception $e) {
            // Cleanup on failure
            try {
                $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}", 'DELETE');
            } catch (\Exception $cleanupException) {
                // Ignore cleanup errors
            }
            throw $e;
        }
    }

    /**
     * Get next available VM ID
     */
    private function getNextVmId($node): int
    {
        $response = $this->request("/api2/json/nodes/{$node}/qemu");
        $kvmIds = array_column($response['data'], 'vmid');

        $response = $this->request("/api2/json/nodes/{$node}/lxc");
        $lxcIds = array_column($response['data'], 'vmid');

        $usedIds = array_merge($kvmIds, $lxcIds);

        // Start from 100 and find first available ID
        for ($id = 100; $id < 999999; $id++) {
            if (!in_array($id, $usedIds)) {
                return $id;
            }
        }

        throw new \Exception('No available VM ID found');
    }

    /**
     * Prepare network configuration
     */
    private function prepareNetworkConfig($config): array
    {
        $networkType = $config['network_type'] ?? 'public';
        $networks = [];

        if ($networkType === 'public' || $networkType === 'both') {
            $networks[] = [
                'bridge' => $this->config('public_bridge'),
                'type' => 'public',
                'ip' => $this->getNextAvailableIp('public'),
                'gateway' => $this->config('public_gateway'),
                'subnet' => $this->config('public_subnet'),
            ];
        }

        if ($networkType === 'private' || $networkType === 'both') {
            $networks[] = [
                'bridge' => $this->config('private_bridge'),
                'type' => 'private',
                'ip' => $this->getNextAvailableIp('private'),
                'gateway' => $this->config('private_gateway'),
                'subnet' => $this->config('private_subnet'),
            ];
        }

        return $networks;
    }

    /**
     * Get next available IP address
     */
    private function getNextAvailableIp($type): string
    {
        $range = $this->config($type . '_ip_range');
        if (empty($range)) {
            // Generate IP from subnet if range not specified
            $subnet = $this->config($type . '_subnet');
            $parts = explode('/', $subnet);
            $network = $parts[0];
            $networkParts = explode('.', $network);
            return $networkParts[0] . '.' . $networkParts[1] . '.' . $networkParts[2] . '.' . (100 + rand(1, 50));
        }

        // Parse range (e.g., "***********00-*************")
        $rangeParts = explode('-', $range);
        $startIp = trim($rangeParts[0]);
        $endIp = trim($rangeParts[1]);

        // For simplicity, return a random IP in range
        $startParts = explode('.', $startIp);
        $endParts = explode('.', $endIp);

        $randomLast = rand((int)$startParts[3], (int)$endParts[3]);
        return $startParts[0] . '.' . $startParts[1] . '.' . $startParts[2] . '.' . $randomLast;
    }

    /**
     * Create LXC container
     */
    private function createLxcContainer($service, $config, $vmId, $node, $networks): array
    {
        $hostname = $config['hostname'] ?? 'vm-' . $service->id;
        $template = $config['template'] ?? 'ubuntu-22.04';
        $password = $config['root_password'];
        $memory = $config['memory'] ?? 1024;
        $disk = $config['disk_size'] ?? 20;
        $cores = $config['cpu_cores'] ?? 1;

        // Prepare network string for LXC
        $netConfig = '';
        if (!empty($networks)) {
            $network = $networks[0]; // Use first network
            $netConfig = "name=eth0,bridge={$network['bridge']},ip={$network['ip']}/24,gw={$network['gateway']}";
        }

        $data = [
            'vmid' => $vmId,
            'hostname' => $hostname,
            'ostemplate' => $this->config('lxc_template_storage') . ':vztmpl/' . $template,
            'password' => $password,
            'memory' => $memory,
            'rootfs' => $this->config('storage') . ':' . $disk,
            'cores' => $cores,
            'net0' => $netConfig,
            'nameserver' => $this->config('dns_server'),
            'start' => 1,
        ];

        if (!empty($config['ssh_key'])) {
            $data['ssh-public-keys'] = $config['ssh_key'];
        }

        $response = $this->request("/api2/json/nodes/{$node}/lxc", 'POST', $data);

        return [
            'ip' => $networks[0]['ip'] ?? '',
            'task' => $response['data'] ?? '',
        ];
    }

    /**
     * Create KVM virtual machine
     */
    private function createKvmVm($service, $config, $vmId, $node, $networks): array
    {
        $hostname = $config['hostname'] ?? 'vm-' . $service->id;
        $memory = $config['memory'] ?? 1024;
        $disk = $config['disk_size'] ?? 20;
        $cores = $config['cpu_cores'] ?? 1;

        // Prepare network string for KVM
        $netConfig = '';
        if (!empty($networks)) {
            $network = $networks[0]; // Use first network
            $netConfig = "virtio,bridge={$network['bridge']}";
        }

        $data = [
            'vmid' => $vmId,
            'name' => $hostname,
            'memory' => $memory,
            'cores' => $cores,
            'net0' => $netConfig,
            'scsi0' => $this->config('storage') . ':' . $disk,
            'scsihw' => 'virtio-scsi-pci',
            'ostype' => 'l26', // Linux 2.6+
            'boot' => 'order=scsi0',
        ];

        $response = $this->request("/api2/json/nodes/{$node}/qemu", 'POST', $data);

        return [
            'ip' => $networks[0]['ip'] ?? '',
            'task' => $response['data'] ?? '',
        ];
    }

    /**
     * Setup reverse proxy
     */
    private function setupProxy($service, $config, $ip): void
    {
        if (empty($this->config('proxy_api_url'))) {
            return;
        }

        $proxyData = [
            'domain' => $config['domain'],
            'target' => $ip . ':80', // Default to port 80
            'ssl' => $config['ssl_enabled'] ?? false,
        ];

        try {
            Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->config('proxy_token'),
                'Content-Type' => 'application/json',
            ])->post($this->config('proxy_api_url') . '/api/proxy', $proxyData);

            // Setup port forwarding if specified
            if (!empty($config['port_forwards'])) {
                foreach ($config['port_forwards'] as $port) {
                    $portData = [
                        'port' => (int)$port,
                        'target' => $ip . ':' . $port,
                    ];

                    Http::withHeaders([
                        'Authorization' => 'Bearer ' . $this->config('proxy_token'),
                        'Content-Type' => 'application/json',
                    ])->post($this->config('proxy_api_url') . '/api/port-forward', $portData);
                }
            }
        } catch (\Exception $e) {
            // Log proxy setup error but don't fail server creation
            \Log::warning('Proxy setup failed: ' . $e->getMessage());
        }
    }

    /**
     * Get server information
     */
    private function getServer($serviceId, $failIfNotFound = true, $raw = false)
    {
        $service = Service::find($serviceId);
        if (!$service) {
            if ($failIfNotFound) {
                throw new \Exception('Service not found');
            }
            return false;
        }

        $vmId = $service->properties()->where('key', 'vm_id')->first()?->value;
        $vmType = $service->properties()->where('key', 'vm_type')->first()?->value ?? 'kvm';
        $node = $service->properties()->where('key', 'node')->first()?->value ?? $this->config('default_node');

        if (!$vmId) {
            if ($failIfNotFound) {
                throw new \Exception('VM ID not found');
            }
            return false;
        }

        try {
            $response = $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}/status/current");

            if ($raw) {
                return array_merge($response['data'], [
                    'vm_id' => $vmId,
                    'vm_type' => $vmType,
                    'node' => $node,
                ]);
            }

            return $vmId;
        } catch (\Exception $e) {
            if ($failIfNotFound) {
                throw new \Exception('Server not found: ' . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Suspend server
     */
    public function suspendServer(Service $service, $settings, $properties)
    {
        $server = $this->getServer($service->id, raw: true);
        $vmType = $server['vm_type'];
        $node = $server['node'];
        $vmId = $server['vm_id'];

        $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}/status/stop", 'POST');

        return true;
    }

    /**
     * Unsuspend server
     */
    public function unsuspendServer(Service $service, $settings, $properties)
    {
        $server = $this->getServer($service->id, raw: true);
        $vmType = $server['vm_type'];
        $node = $server['node'];
        $vmId = $server['vm_id'];

        $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}/status/start", 'POST');

        return true;
    }

    /**
     * Terminate server
     */
    public function terminateServer(Service $service, $settings, $properties)
    {
        $server = $this->getServer($service->id, raw: true);
        $vmType = $server['vm_type'];
        $node = $server['node'];
        $vmId = $server['vm_id'];

        // Stop the VM first
        try {
            $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}/status/stop", 'POST');
            sleep(5); // Wait for shutdown
        } catch (\Exception $e) {
            // VM might already be stopped
        }

        // Delete the VM
        $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}", 'DELETE');

        return true;
    }

    /**
     * Upgrade server
     */
    public function upgradeServer(Service $service, $settings, $properties)
    {
        $server = $this->getServer($service->id, raw: true);
        $vmType = $server['vm_type'];
        $node = $server['node'];
        $vmId = $server['vm_id'];

        $config = array_merge($settings, $properties);

        // Update VM configuration
        $updateData = [];

        if (isset($config['memory'])) {
            $updateData['memory'] = (int)$config['memory'];
        }

        if (isset($config['cpu_cores'])) {
            $updateData['cores'] = (int)$config['cpu_cores'];
        }

        if (isset($config['disk_size'])) {
            // Disk resizing requires special handling
            $currentDisk = $server['maxdisk'] ?? 0;
            $newDiskSize = (int)$config['disk_size'] * 1024 * 1024 * 1024; // Convert GB to bytes

            if ($newDiskSize > $currentDisk) {
                $diskKey = $vmType === 'lxc' ? 'rootfs' : 'scsi0';
                $updateData[$diskKey] = $this->config('storage') . ':' . $config['disk_size'];
            }
        }

        if (!empty($updateData)) {
            $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}/config", 'PUT', $updateData);
        }

        return true;
    }

    /**
     * Get available actions for the service
     */
    public function getActions(Service $service)
    {
        $server = $this->getServer($service->id, raw: true);
        $vmType = $server['vm_type'];
        $node = $server['node'];
        $vmId = $server['vm_id'];

        $actions = [
            [
                'type' => 'button',
                'label' => 'Start',
                'function' => 'startServer',
                'color' => 'success',
            ],
            [
                'type' => 'button',
                'label' => 'Stop',
                'function' => 'stopServer',
                'color' => 'danger',
            ],
            [
                'type' => 'button',
                'label' => 'Restart',
                'function' => 'restartServer',
                'color' => 'warning',
            ],
            [
                'type' => 'view',
                'label' => 'Console',
                'function' => 'getConsoleView',
            ],
            [
                'type' => 'view',
                'label' => 'Reinstall',
                'function' => 'getReinstallView',
            ],
            [
                'type' => 'button',
                'label' => 'Reinstall Server',
                'function' => 'reinstallServer',
                'color' => 'danger',
                'confirm' => true,
            ],
        ];

        // Add Proxmox console link if available
        if ($this->config('host')) {
            $consoleUrl = rtrim($this->config('host'), '/') . "/#v1:0:18:4:::::::{$node}%2F{$vmType}%2F{$vmId}:::";
            $actions[] = [
                'type' => 'button',
                'label' => 'Proxmox Console',
                'url' => $consoleUrl,
                'target' => '_blank',
            ];
        }

        return $actions;
    }

    /**
     * Start server action
     */
    public function startServer(Service $service)
    {
        return $this->unsuspendServer($service, [], []);
    }

    /**
     * Stop server action
     */
    public function stopServer(Service $service)
    {
        return $this->suspendServer($service, [], []);
    }

    /**
     * Restart server action
     */
    public function restartServer(Service $service)
    {
        $server = $this->getServer($service->id, raw: true);
        $vmType = $server['vm_type'];
        $node = $server['node'];
        $vmId = $server['vm_id'];

        $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}/status/reboot", 'POST');

        return true;
    }

    /**
     * Get console view
     */
    public function getConsoleView(Service $service, $settings, $properties, $view)
    {
        $server = $this->getServer($service->id, raw: true);

        return view('extension::console', [
            'service' => $service,
            'server' => $server,
            'console_url' => $this->getConsoleUrl($server),
        ]);
    }

    /**
     * Get reinstall view
     */
    public function getReinstallView(Service $service, $settings, $properties, $view)
    {
        return view('extension::reinstall', [
            'service' => $service,
            'templates' => $this->getAvailableTemplates($service),
        ]);
    }

    /**
     * Get console URL
     */
    private function getConsoleUrl($server): string
    {
        $node = $server['node'];
        $vmType = $server['vm_type'];
        $vmId = $server['vm_id'];

        return rtrim($this->config('host'), '/') . "/#v1:0:18:4:::::::{$node}%2F{$vmType}%2F{$vmId}:::";
    }

    /**
     * Get available templates for reinstall
     */
    private function getAvailableTemplates($service): array
    {
        $vmType = $service->properties()->where('key', 'vm_type')->first()?->value ?? 'kvm';

        if ($vmType === 'lxc') {
            try {
                $templateList = $this->request('/api2/json/nodes/' . $this->config('default_node') . '/aplinfo');
                $templates = [];
                foreach ($templateList['data'] as $template) {
                    $templates[$template['template']] = $template['headline'] ?? $template['template'];
                }
                return $templates;
            } catch (\Exception $e) {
                // Fallback templates
            }
        }

        return [
            'ubuntu-20.04' => 'Ubuntu 20.04 LTS',
            'ubuntu-22.04' => 'Ubuntu 22.04 LTS',
            'debian-11' => 'Debian 11',
            'debian-12' => 'Debian 12',
            'centos-7' => 'CentOS 7',
            'centos-8' => 'CentOS 8',
        ];
    }

    /**
     * Reinstall server with new template
     */
    public function reinstallServer(Service $service, $settings, $properties)
    {
        $server = $this->getServer($service->id, raw: true);
        $vmType = $server['vm_type'];
        $node = $server['node'];
        $vmId = $server['vm_id'];

        // Get reinstall parameters
        $template = $properties['template'] ?? $settings['template'] ?? 'ubuntu-22.04';
        $rootPassword = $properties['root_password'] ?? $settings['root_password'];
        $sshKey = $properties['ssh_key'] ?? $settings['ssh_key'] ?? '';

        if (empty($rootPassword)) {
            throw new \Exception('Root password is required for reinstall');
        }

        try {
            // Stop the VM first
            try {
                $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}/status/stop", 'POST');
                sleep(10); // Wait for shutdown
            } catch (\Exception $e) {
                // VM might already be stopped
            }

            // Get current VM configuration to preserve settings
            $currentConfig = $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}/config");
            $config = $currentConfig['data'];

            // Delete the VM
            $this->request("/api2/json/nodes/{$node}/{$vmType}/{$vmId}", 'DELETE');
            sleep(5); // Wait for deletion

            // Recreate VM with same ID and preserved settings
            if ($vmType === 'lxc') {
                $this->recreateLxcContainer($service, $vmId, $node, $config, $template, $rootPassword, $sshKey);
            } else {
                $this->recreateKvmVm($service, $vmId, $node, $config, $template, $rootPassword, $sshKey);
            }

            // Update service properties
            $service->properties()->updateOrCreate(['key' => 'template'], [
                'name' => 'Template',
                'value' => $template,
            ]);

            if (!empty($sshKey)) {
                $service->properties()->updateOrCreate(['key' => 'ssh_key'], [
                    'name' => 'SSH Key',
                    'value' => $sshKey,
                ]);
            }

            return [
                'status' => 'reinstalled',
                'template' => $template,
                'vm_id' => $vmId,
            ];

        } catch (\Exception $e) {
            throw new \Exception('Reinstall failed: ' . $e->getMessage());
        }
    }

    /**
     * Recreate LXC container with preserved settings
     */
    private function recreateLxcContainer($service, $vmId, $node, $oldConfig, $template, $password, $sshKey): void
    {
        $hostname = $service->properties()->where('key', 'hostname')->first()?->value ?? 'vm-' . $service->id;

        $data = [
            'vmid' => $vmId,
            'hostname' => $hostname,
            'ostemplate' => $this->config('lxc_template_storage') . ':vztmpl/' . $template,
            'password' => $password,
            'memory' => $oldConfig['memory'] ?? 1024,
            'rootfs' => $oldConfig['rootfs'] ?? $this->config('storage') . ':20',
            'cores' => $oldConfig['cores'] ?? 1,
            'net0' => $oldConfig['net0'] ?? '',
            'nameserver' => $this->config('dns_server'),
            'start' => 1,
        ];

        if (!empty($sshKey)) {
            $data['ssh-public-keys'] = $sshKey;
        }

        $this->request("/api2/json/nodes/{$node}/lxc", 'POST', $data);
    }

    /**
     * Recreate KVM VM with preserved settings
     */
    private function recreateKvmVm($service, $vmId, $node, $oldConfig, $template, $password, $sshKey): void
    {
        $hostname = $service->properties()->where('key', 'hostname')->first()?->value ?? 'vm-' . $service->id;

        $data = [
            'vmid' => $vmId,
            'name' => $hostname,
            'memory' => $oldConfig['memory'] ?? 1024,
            'cores' => $oldConfig['cores'] ?? 1,
            'net0' => $oldConfig['net0'] ?? '',
            'scsi0' => $oldConfig['scsi0'] ?? $this->config('storage') . ':20',
            'scsihw' => 'virtio-scsi-pci',
            'ostype' => 'l26', // Linux 2.6+
            'boot' => 'order=scsi0',
        ];

        $this->request("/api2/json/nodes/{$node}/qemu", 'POST', $data);

        // For KVM, we need to handle the template/ISO mounting separately
        // This is a simplified approach - in production you might want to use cloud-init
    }
}
