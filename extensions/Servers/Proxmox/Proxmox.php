<?php

namespace Paymenter\Extensions\Servers\Proxmox;

use App\Classes\Extension\Server;
use App\Models\Product;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class Proxmox extends Server
{
    private $authTicket = null;
    private $ticketExpiry = null;

    private function request($endpoint, $method = 'GET', $data = []): array
    {
        $url = 'https://' . $this->config('ip') . ':' . $this->config('port') . '/api2/json' . $endpoint;
        
        // Get authentication ticket
        $ticket = $this->getAuthTicket();
        
        $headers = [
            'Cookie' => 'PVEAuthCookie=' . $ticket['ticket'],
            'CSRFPreventionToken' => $ticket['CSRFPreventionToken'],
        ];

        if ($method === 'GET') {
            $response = Http::withoutVerifying()
                ->withHeaders($headers)
                ->get($url, $data)
                ->throw();
        } else {
            $response = Http::withoutVerifying()
                ->withHeaders($headers)
                ->asForm()
                ->$method($url, $data)
                ->throw();
        }

        if (!$response->successful()) {
            throw new \Exception('Failed to connect to Proxmox API: ' . $response->body());
        }

        return $response->json();
    }

    private function getAuthTicket(): array
    {
        // Check if ticket is still valid (cache for 1 hour)
        if ($this->authTicket && $this->ticketExpiry > time()) {
            return $this->authTicket;
        }

        $authUrl = 'https://' . $this->config('ip') . ':' . $this->config('port') . '/api2/json/access/ticket';

        try {
            $response = Http::withoutVerifying()
                ->asForm()
                ->post($authUrl, [
                    'username' => $this->config('username'),
                    'password' => $this->config('password'),
                    'realm' => $this->config('realm', 'pve'),
                ])
                ->throw();

            if (!$response->successful()) {
                throw new \Exception('Failed to authenticate with Proxmox');
            }

            $data = $response->json();
            $this->authTicket = $data['data'];

            // Cache for 1 hour (Proxmox default ticket lifetime is 2 hours)
            $this->ticketExpiry = time() + 3600;

            return $this->authTicket;
        } catch (\Exception $e) {
            \Log::error('Proxmox authentication failed', [
                'proxmox_ip' => $this->config('ip'),
                'username' => $this->config('username'),
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to authenticate with Proxmox: ' . $e->getMessage());
        }
    }

    private function getNextVmid(): int
    {
        $response = $this->request('/cluster/nextid');
        return (int) $response['data'];
    }

    private function waitForTask($upid, $timeoutSeconds = 300): bool
    {
        $maxAttempts = $timeoutSeconds / 5; // 5 second intervals
        $attempts = 0;

        while ($attempts < $maxAttempts) {
            $response = $this->request('/nodes/' . $this->config('node') . '/tasks/' . $upid . '/status');
            $status = $response['data']['status'];

            if ($status === 'stopped') {
                $exitStatus = $response['data']['exitstatus'] ?? null;
                return $exitStatus === 'OK' || $exitStatus === null;
            }

            sleep(5);
            $attempts++;
        }

        return false;
    }

    private function getTaskStatus($upid): array
    {
        try {
            $response = $this->request('/nodes/' . $this->config('node') . '/tasks/' . $upid . '/status');
            return $response['data'] ?? [];
        } catch (\Exception $e) {
            return [];
        }
    }

    private function parseIpRange($range): array
    {
        if (!$range) {
            return [];
        }
        
        $parts = explode(' - ', trim($range));
        if (count($parts) !== 2) {
            throw new \Exception('Invalid IP range format. Use: ************* - *************');
        }
        
        return [
            'start' => ip2long(trim($parts[0])),
            'end' => ip2long(trim($parts[1]))
        ];
    }

    private function getUsedIps(): array
    {
        $services = Service::whereHas('properties', function($query) {
            $query->where('key', 'allocated_ip');
        })->with('properties')->get();
        
        $usedIps = [];
        foreach ($services as $service) {
            $ip = $service->properties()->where('key', 'allocated_ip')->first();
            if ($ip) {
                $usedIps[] = $ip->value;
            }
        }
        
        return $usedIps;
    }

    private function findAvailableIp($networkType = 'public'): ?string
    {
        $rangeKey = $networkType === 'public' ? 'public_ip_range' : 'private_ip_range';
        $range = $this->parseIpRange($this->config($rangeKey));
        
        if (empty($range)) {
            return null;
        }
        
        $usedIps = $this->getUsedIps();
        
        for ($ip = $range['start']; $ip <= $range['end']; $ip++) {
            $ipString = long2ip($ip);
            if (!in_array($ipString, $usedIps)) {
                return $ipString;
            }
        }
        
        return null;
    }

    private function validateTemplate(string $template, string $vmType): bool
    {
        try {
            if ($vmType === 'qemu') {
                $response = $this->request('/nodes/' . $this->config('node') . '/qemu/' . $template . '/config');
                return isset($response['data']['template']) && $response['data']['template'] == 1;
            } else {
                $storage = $this->config('lxc_storage') ?: $this->config('storage');
                $response = $this->request('/nodes/' . $this->config('node') . '/storage/' . $storage . '/content');

                foreach ($response['data'] as $item) {
                    if ($item['volid'] === $template && $item['content'] === 'vztmpl') {
                        return true;
                    }
                }
            }
            return false;
        } catch (\Exception $e) {
            \Log::warning('Template validation failed', [
                'template' => $template,
                'vm_type' => $vmType,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function handleProxmoxError(\Exception $e, string $operation, array $context = []): void
    {
        $errorMessage = "Proxmox {$operation} failed: " . $e->getMessage();

        \Log::error($errorMessage, array_merge([
            'operation' => $operation,
            'proxmox_ip' => $this->config('ip'),
            'error' => $e->getMessage(),
        ], $context));

        throw new \Exception($errorMessage);
    }



    private function formatLxcTemplateName(string $templateName): string
    {
        // Remove file extensions
        $name = preg_replace('/\.(tar\.gz|tar\.xz|tar\.zst|tar\.bz2)$/', '', $templateName);

        // Extract meaningful part from template names like:
        // alpine-3.19-default_20240207_amd64 -> alpine-3.19
        // debian-11-standard_11.7-1_amd64 -> debian-11
        // ubuntu-22.04-standard_22.04-1_amd64 -> ubuntu-22.04

        if (preg_match('/^(alpine-\d+\.\d+)/', $name, $matches)) {
            return $matches[1];
        }

        if (preg_match('/^(debian-\d+)/', $name, $matches)) {
            return $matches[1];
        }

        if (preg_match('/^(ubuntu-\d+\.\d+)/', $name, $matches)) {
            return $matches[1];
        }

        if (preg_match('/^(centos-\d+)/', $name, $matches)) {
            return $matches[1];
        }

        if (preg_match('/^(fedora-\d+)/', $name, $matches)) {
            return $matches[1];
        }

        // For other templates, try to extract OS name and version
        if (preg_match('/^([a-zA-Z]+)-?(\d+(?:\.\d+)?)/', $name, $matches)) {
            return $matches[1] . (isset($matches[2]) ? '-' . $matches[2] : '');
        }

        // Fallback: return first part before underscore or dash
        $parts = preg_split('/[_-]/', $name);
        return $parts[0] . (isset($parts[1]) && is_numeric($parts[1][0]) ? '-' . $parts[1] : '');
    }

    /**
     * Get all the configuration for the extension
     */
    public function getConfig($values = []): array
    {
        return [
            [
                'name' => 'ip',
                'type' => 'text',
                'label' => 'Proxmox Server IP',
                'required' => true,
            ],
            [
                'name' => 'port',
                'type' => 'text',
                'label' => 'Port',
                'required' => true,
                'default' => '8006',
            ],
            [
                'name' => 'username',
                'type' => 'text',
                'label' => 'Username',
                'required' => true,
                'default' => 'root@pam',
            ],
            [
                'name' => 'password',
                'type' => 'password',
                'label' => 'Password',
                'required' => true,
            ],
            [
                'name' => 'realm',
                'type' => 'text',
                'label' => 'Realm',
                'default' => 'pve',
                'required' => true,
            ],
            [
                'name' => 'node',
                'type' => 'text',
                'label' => 'Default Node',
                'required' => true,
            ],
            [
                'name' => 'storage',
                'type' => 'text',
                'label' => 'Storage',
                'required' => true,
                'default' => 'local-lvm',
            ],
            [
                'name' => 'lxc_storage',
                'type' => 'text',
                'label' => 'LXC Template Storage',
                'required' => false,
                'description' => 'Storage for LXC templates (optional, defaults to main storage)',
            ],
            [
                'name' => 'public_bridge',
                'type' => 'text',
                'label' => 'Public Network Bridge',
                'required' => true,
                'default' => 'vmbr0',
            ],
            [
                'name' => 'public_ip_range',
                'type' => 'text',
                'label' => 'Public IP Range',
                'required' => true,
                'placeholder' => '************* - *************',
                'description' => 'Format: start_ip - end_ip',
            ],
            [
                'name' => 'public_subnet',
                'type' => 'text',
                'label' => 'Public Subnet (CIDR)',
                'required' => true,
                'default' => '24',
            ],
            [
                'name' => 'public_gateway',
                'type' => 'text',
                'label' => 'Public Gateway',
                'required' => true,
            ],
            [
                'name' => 'private_bridge',
                'type' => 'text',
                'label' => 'Private Network Bridge',
                'required' => false,
            ],
            [
                'name' => 'private_ip_range',
                'type' => 'text',
                'label' => 'Private IP Range',
                'required' => false,
                'placeholder' => '********** - **********',
            ],
            [
                'name' => 'private_subnet',
                'type' => 'text',
                'label' => 'Private Subnet (CIDR)',
                'required' => false,
                'default' => '24',
            ],
            [
                'name' => 'private_gateway',
                'type' => 'text',
                'label' => 'Private Gateway',
                'required' => false,
            ],
            [
                'name' => 'dns_servers',
                'type' => 'text',
                'label' => 'DNS Servers',
                'required' => false,
                'placeholder' => '******* *******',
                'description' => 'Space-separated DNS servers',
            ],
        ];
    }

    /**
     * Get product config
     */
    public function getProductConfig($values = []): array
    {
        return [
            [
                'name' => 'vm_type',
                'label' => 'VM Type',
                'type' => 'select',
                'required' => true,
                'options' => [
                    'qemu' => 'KVM/QEMU',
                    'lxc' => 'LXC Container',
                ],
            ],
            [
                'name' => 'cores',
                'label' => 'CPU Cores',
                'type' => 'number',
                'required' => true,
                'default' => 1,
            ],
            [
                'name' => 'memory',
                'label' => 'Memory (MB)',
                'type' => 'number',
                'required' => true,
                'default' => 512,
            ],
            [
                'name' => 'disk_size',
                'label' => 'Disk Size (GB)',
                'type' => 'number',
                'required' => true,
                'default' => 10,
            ],
            [
                'name' => 'bandwidth',
                'label' => 'Bandwidth Limit (MB/s)',
                'type' => 'number',
                'required' => false,
                'default' => 0,
                'description' => 'Set to 0 for unlimited',
            ],
            [
                'name' => 'network_type',
                'label' => 'Network Type',
                'type' => 'select',
                'required' => true,
                'options' => [
                    'public' => 'Public Network',
                    'private' => 'Private Network',
                ],
                'default' => 'public',
            ],
        ];
    }

    public function getCheckoutConfig(Product $product)
    {
        $vmType = $product->settings()->where('key', 'vm_type')->first()->value;
        $templates = [];

        try {
            if ($vmType === 'qemu') {
                // Get QEMU templates with consistent ordering
                $response = $this->request('/nodes/' . $this->config('node') . '/qemu');
                $templateList = [];

                foreach ($response['data'] as $vm) {
                    if (isset($vm['template']) && $vm['template'] == 1) {
                        $templateList[] = [
                            'vmid' => $vm['vmid'],
                            'name' => $vm['name']
                        ];
                    }
                }

                // Sort templates by name for consistent ordering
                usort($templateList, function($a, $b) {
                    return strcmp($a['name'], $b['name']);
                });

                // Create templates with vmid as key and name as display
                foreach ($templateList as $template) {
                    $templates[$template['vmid']] = $template['name'];
                }
            } else {
                // Get LXC templates
                $storage = $this->config('lxc_storage') ?: $this->config('storage');
                $response = $this->request('/nodes/' . $this->config('node') . '/storage/' . $storage . '/content');
                foreach ($response['data'] as $template) {
                    if ($template['content'] === 'vztmpl') {
                        $templateName = basename($template['volid']);
                        $displayName = $this->formatLxcTemplateName($templateName);
                        $templates[$template['volid']] = $displayName;
                    }
                }
            }
        } catch (\Exception $e) {
            // If we can't get templates, provide empty array
            $templates = [];
        }

        return [
            [
                'name' => 'hostname',
                'type' => 'text',
                'validation' => 'regex:/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/i',
                'label' => 'Hostname',
                'placeholder' => 'app, db-srv, or example.com',
                'required' => true,
            ],
            [
                'name' => 'template',
                'type' => 'select',
                'friendlyName' => 'Template',
                'required' => true,
                'options' => $templates,
                'default' => '', // Ensure no default selection
                'placeholder' => 'Select a template...',
            ],
            [
                'name' => 'password',
                'type' => 'password',
                'label' => 'Root Password',
                'required' => false,
                'description' => 'Leave empty for auto-generated password',
            ],
            [
                'name' => 'ssh_key',
                'type' => 'textarea',
                'label' => 'SSH Public Key (Optional)',
                'required' => false,
                'description' => 'SSH public key for key-based authentication',
                'placeholder' => 'ssh-rsa AAAAB3NzaC1yc2EAAAA...',
            ],
            [
                'name' => 'enable_proxy',
                'type' => 'toggle',
                'label' => 'Enable Reverse Proxy',
                'required' => false,
                'description' => 'Enable reverse proxy management',
            ],
            [
                'name' => 'ssl_enabled',
                'type' => 'toggle',
                'label' => 'Enable SSL',
                'required' => false,
                'description' => 'Enable SSL certificate (requires domain)',
                'show_if' => 'enable_proxy',
            ],
            [
                'name' => 'domain',
                'type' => 'text',
                'label' => 'Domain',
                'required' => false,
                'description' => 'Domain name for proxy (required if proxy enabled)',
                'validation' => 'regex:/^[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}$/',
                'show_if' => 'enable_proxy',
                'placeholder' => 'example.com',
            ],
            [
                'name' => 'enable_port_forward',
                'type' => 'toggle',
                'label' => 'Enable Port Forwarding',
                'required' => false,
                'description' => 'Enable custom port forwarding',
            ],
            [
                'name' => 'port_forwards',
                'type' => 'dynamic_list',
                'label' => 'Port Forwarding',
                'required' => false,
                'description' => 'Ports to forward (e.g., 22, 80, 443)',
                'show_if' => 'enable_port_forward',
                'item_type' => 'number',
                'item_label' => 'Port',
                'min_items' => 0,
                'max_items' => 10,
                'placeholder' => '80',
            ],
        ];
    }

    /**
     * Check if current configuration is valid
     */
    public function testConfig(): bool|string
    {
        try {
            $this->request('/version');
        } catch (\Exception $e) {
            return $e->getMessage();
        }

        return true;
    }

    /**
     * Create a server
     */
    public function createServer(Service $service, $settings, $properties)
    {
        $settings = array_merge($settings, $properties);

        // Validate template before proceeding
        if (!$this->validateTemplate($settings['template'], $settings['vm_type'])) {
            throw new \Exception('Selected template is not available or invalid');
        }

        // Get next available VMID
        $vmid = $this->getNextVmid();

        // Generate password if not provided
        $password = $settings['password'] ?: Str::random(12);
        
        // Allocate IP address
        $allocatedIp = $this->findAvailableIp($settings['network_type']);
        if (!$allocatedIp) {
            throw new \Exception('No available IP addresses in the configured range');
        }
        
        // Get network configuration
        $networkType = $settings['network_type'];
        $bridge = $networkType === 'public' ? $this->config('public_bridge') : $this->config('private_bridge');
        $subnet = $networkType === 'public' ? $this->config('public_subnet') : $this->config('private_subnet');
        $gateway = $networkType === 'public' ? $this->config('public_gateway') : $this->config('private_gateway');
        
        if ($settings['vm_type'] === 'qemu') {
            // Create QEMU VM by cloning template
            $cloneData = [
                'newid' => $vmid,
                'name' => $settings['hostname'],
                'full' => 1,
                'target' => $this->config('node'),
            ];
            
            $response = $this->request('/nodes/' . $this->config('node') . '/qemu/' . $settings['template'] . '/clone', 'POST', $cloneData);
            
            if (!$this->waitForTask($response['data'])) {
                throw new \Exception('Failed to clone VM template');
            }
            
            // Configure the VM
            $configData = [
                'cores' => $settings['cores'],
                'memory' => $settings['memory'],
                'ciuser' => 'root',
                'ipconfig0' => "ip={$allocatedIp}/{$subnet},gw={$gateway}",
                'net0' => "virtio,bridge={$bridge}",
            ];
            
            if ($password) {
                $configData['cipassword'] = $password;
            }

            // Add SSH key if provided
            if (!empty($settings['ssh_key'])) {
                $configData['sshkeys'] = urlencode($settings['ssh_key']);
            }

            if ($settings['bandwidth'] > 0) {
                $configData['net0'] .= ",rate={$settings['bandwidth']}";
            }
            
            $response = $this->request('/nodes/' . $this->config('node') . '/qemu/' . $vmid . '/config', 'PUT', $configData);

            // Resize disk if needed
            if (isset($settings['disk_size']) && $settings['disk_size'] > 0) {
                try {
                    // Wait a bit for VM to be fully configured
                    sleep(3);

                    // Get current disk size
                    $currentSize = $this->getCurrentDiskSize($vmid, 'qemu');

                    if ($settings['disk_size'] > $currentSize) {
                        \Log::info("Setting disk size for KVM using bash script approach", [
                            'vmid' => $vmid,
                            'current_size' => $currentSize,
                            'target_size' => $settings['disk_size']
                        ]);

                        // Use bash script approach: set disk size directly in config
                        $storage = $this->config('storage');
                        $this->request('/nodes/' . $this->config('node') . '/qemu/' . $vmid . '/config', 'PUT', [
                            'scsi0' => $storage . ':' . $settings['disk_size']
                        ]);

                        \Log::info("Disk size set successfully using config approach", [
                            'vmid' => $vmid,
                            'disk_size' => $settings['disk_size'] . 'GB',
                            'storage' => $storage
                        ]);
                    }
                } catch (\Exception $e) {
                    \Log::error("Failed to resize disk during KVM creation", [
                        'vmid' => $vmid,
                        'error' => $e->getMessage()
                    ]);
                    // Don't fail the entire creation process for disk resize
                }
            }

            // Start the VM
            $this->request('/nodes/' . $this->config('node') . '/qemu/' . $vmid . '/status/start', 'POST');
            
        } else {
            // Create LXC Container
            $storage = $this->config('storage');
            $rootfs = $storage . ':' . $settings['disk_size'];
            
            $createData = [
                'vmid' => $vmid,
                'hostname' => $settings['hostname'],
                'cores' => $settings['cores'],
                'memory' => $settings['memory'],
                'rootfs' => $rootfs,
                'net0' => "name=eth0,bridge={$bridge},ip={$allocatedIp}/{$subnet},gw={$gateway}",
                'ostemplate' => $settings['template'],
                'unprivileged' => 1,
            ];
            
            if ($password) {
                $createData['password'] = $password;
            }

            // Add SSH key if provided
            if (!empty($settings['ssh_key'])) {
                $createData['ssh-public-keys'] = $settings['ssh_key'];
            }

            if ($settings['bandwidth'] > 0) {
                $createData['net0'] .= ",rate={$settings['bandwidth']}";
            }
            
            if ($this->config('dns_servers')) {
                $createData['nameserver'] = str_replace(' ', ',', $this->config('dns_servers'));
            }
            
            $response = $this->request('/nodes/' . $this->config('node') . '/lxc', 'POST', $createData);
            
            if (!$this->waitForTask($response['data'])) {
                throw new \Exception('Failed to create LXC container');
            }
            
            // Start the container
            $this->request('/nodes/' . $this->config('node') . '/lxc/' . $vmid . '/status/start', 'POST');
        }
        
        // Store service properties
        $service->properties()->updateOrCreate([
            'key' => 'vmid',
        ], [
            'name' => 'VM ID',
            'value' => $vmid,
        ]);

        $service->properties()->updateOrCreate([
            'key' => 'vm_type',
        ], [
            'name' => 'VM Type',
            'value' => $settings['vm_type'],
        ]);

        $service->properties()->updateOrCreate([
            'key' => 'allocated_ip',
        ], [
            'name' => 'Allocated IP',
            'value' => $allocatedIp,
        ]);

        $service->properties()->updateOrCreate([
            'key' => 'hostname',
        ], [
            'name' => 'Hostname',
            'value' => $settings['hostname'],
        ]);

        $service->properties()->updateOrCreate([
            'key' => 'template',
        ], [
            'name' => 'Template',
            'value' => $settings['template'],
        ]);

        // Only store SSH key if it's provided and not empty
        if (!empty($settings['ssh_key'])) {
            $service->properties()->updateOrCreate([
                'key' => 'ssh_key',
            ], [
                'name' => 'SSH Key',
                'value' => $settings['ssh_key'],
            ]);
        }

        // Store proxy settings if enabled
        if (!empty($settings['enable_proxy'])) {
            $service->properties()->updateOrCreate([
                'key' => 'enable_proxy',
            ], [
                'name' => 'Enable Proxy',
                'value' => '1',
            ]);

            if (!empty($settings['domain'])) {
                $service->properties()->updateOrCreate([
                    'key' => 'domain',
                ], [
                    'name' => 'Domain',
                    'value' => $settings['domain'],
                ]);
            }

            if (!empty($settings['ssl_enabled'])) {
                $service->properties()->updateOrCreate([
                    'key' => 'ssl_enabled',
                ], [
                    'name' => 'SSL Enabled',
                    'value' => '1',
                ]);
            }
        }

        // Store port forwarding settings if enabled
        if (!empty($settings['enable_port_forward']) && !empty($settings['port_forwards'])) {
            $service->properties()->updateOrCreate([
                'key' => 'enable_port_forward',
            ], [
                'name' => 'Enable Port Forward',
                'value' => '1',
            ]);

            $service->properties()->updateOrCreate([
                'key' => 'port_forwards',
            ], [
                'name' => 'Port Forwards',
                'value' => is_array($settings['port_forwards']) ? json_encode($settings['port_forwards']) : $settings['port_forwards'],
            ]);
        }
        
        return [
            'vmid' => $vmid,
            'ip' => $allocatedIp,
            'password' => $password,
        ];
    }

    /**
     * Suspend a server
     */
    public function suspendServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/suspend', 'POST');
        
        return true;
    }

    /**
     * Unsuspend a server
     */
    public function unsuspendServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/resume', 'POST');
        
        return true;
    }

    /**
     * Terminate a server
     */
    public function terminateServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        // Stop the VM/Container first
        try {
            $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/stop', 'POST');
            sleep(5); // Wait for stop to complete
        } catch (\Exception $e) {
            // Continue with deletion even if stop fails
        }
        
        // Delete the VM/Container
        $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'], 'DELETE');
        
        // Remove all properties
        $service->properties()->whereIn('key', ['vmid', 'vm_type', 'allocated_ip'])->delete();
        
        return true;
    }

    /**
     * Get VM current status
     */
    public function getVmStatus(Service $service, $settings, $properties): array
    {
        if (!isset($properties['vmid'])) {
            return ['status' => 'unknown', 'display' => 'Unknown', 'color' => 'secondary'];
        }

        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        $vmid = $properties['vmid'];

        try {
            $response = $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $vmid . '/status/current');
            $status = $response['data']['status'] ?? 'unknown';

            $statusMap = [
                'running' => ['status' => 'running', 'display' => 'Started', 'color' => 'success'],
                'stopped' => ['status' => 'stopped', 'display' => 'Stopped', 'color' => 'danger'],
                'paused' => ['status' => 'paused', 'display' => 'Paused', 'color' => 'warning'],
            ];

            return $statusMap[$status] ?? ['status' => $status, 'display' => ucfirst($status), 'color' => 'secondary'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'display' => 'Error', 'color' => 'danger'];
        }
    }

    public function getActions(Service $service, $settings, $properties): array
    {
        if (!isset($properties['vmid'])) {
            return [];
        }

        // Get current VM status
        $vmStatus = $this->getVmStatus($service, $settings, $properties);

        $actions = [
            [
                'type' => 'info',
                'label' => 'VM Status: ' . $vmStatus['display'],
                'color' => $vmStatus['color'],
                'icon' => $vmStatus['status'] === 'running' ? 'play-circle' : 'stop-circle',
            ],
            [
                'type' => 'button',
                'label' => 'Start VM',
                'function' => 'startVm',
                'color' => 'success',
                'disabled' => $vmStatus['status'] === 'running',
            ],
            [
                'type' => 'button',
                'label' => 'Stop VM',
                'function' => 'stopVm',
                'color' => 'danger',
                'disabled' => $vmStatus['status'] === 'stopped',
            ],
            [
                'type' => 'button',
                'label' => 'Restart VM',
                'function' => 'restartVm',
                'color' => 'warning',
                'disabled' => $vmStatus['status'] === 'stopped',
            ],
            [
                'type' => 'view',
                'label' => 'Console Access',
                'view' => 'extension::console',
                'color' => 'primary',
            ],
            [
                'type' => 'view',
                'label' => 'Reinstall Server',
                'view' => 'extension::reinstall',
                'color' => 'danger',
            ],
        ];

        return $actions;
    }

    /**
     * Get service status for Paymenter UI
     */
    public function getStatus(Service $service, $settings, $properties): array
    {
        if (!isset($properties['vmid'])) {
            return [
                'status' => 'unknown',
                'label' => 'Unknown',
                'color' => 'secondary'
            ];
        }

        $vmStatus = $this->getVmStatus($service, $settings, $properties);

        return [
            'status' => $vmStatus['status'],
            'label' => $vmStatus['display'],
            'color' => $vmStatus['color']
        ];
    }

    /**
     * Alternative method names for status
     */
    public function getServiceStatus(Service $service, $settings, $properties): array
    {
        return $this->getStatus($service, $settings, $properties);
    }

    public function getServerStatus(Service $service, $settings, $properties): array
    {
        return $this->getStatus($service, $settings, $properties);
    }

    /**
     * Get service details for user panel
     */
    public function getServiceDetails(Service $service, $settings, $properties): array
    {
        if (!isset($properties['vmid'])) {
            return [];
        }

        $details = [];

        // VM ID
        $details[] = [
            'label' => 'VM ID',
            'value' => $properties['vmid']
        ];

        // IP Address
        if (isset($properties['allocated_ip'])) {
            $details[] = [
                'label' => 'IP Address',
                'value' => $properties['allocated_ip']
            ];
        }

        // Hostname
        if (isset($properties['hostname'])) {
            $details[] = [
                'label' => 'Hostname',
                'value' => $properties['hostname']
            ];
        }

        // VM Type
        $vmType = $properties['vm_type'] ?? 'qemu';
        $details[] = [
            'label' => 'VM Type',
            'value' => $vmType === 'qemu' ? 'KVM' : 'LXC Container'
        ];

        // VM User (default root)
        $details[] = [
            'label' => 'VM User',
            'value' => 'root'
        ];

        // Resources
        $details[] = [
            'label' => 'CPU Cores',
            'value' => $settings['cores'] ?? 'N/A'
        ];

        $details[] = [
            'label' => 'Memory',
            'value' => ($settings['memory'] ?? 'N/A') . ' MB'
        ];

        $details[] = [
            'label' => 'Disk Size',
            'value' => ($settings['disk_size'] ?? 'N/A') . ' GB'
        ];

        return $details;
    }



    /**
     * Reinstall VM/Container (terminate + create)
     */
    public function reinstallVm(Service $service, $settings, $properties): void
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        // Use existing properties for reinstall
        $hostname = $properties['hostname'] ?? $service->name;
        $template = $properties['template'] ?? null;

        if (!$template) {
            throw new \Exception('No template found for reinstall');
        }

        $vmid = $properties['vmid'];
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';

        try {
            \Log::info("Starting VM/Container reinstallation", [
                'vmid' => $properties['vmid'],
                'service_id' => $service->id,
                'hostname' => $hostname
            ]);

            // Step 1: Terminate existing VM
            $this->terminateServer($service, $settings, $properties);

            \Log::info("VM terminated, waiting before recreation");
            sleep(5); // Wait for complete termination

            // Step 2: Create new VM with same settings
            // Prepare properties for createServer (use existing template and hostname)
            $createProperties = array_merge($properties, [
                'hostname' => $hostname,
                'template' => $template
            ]);

            $this->createServer($service, $settings, $createProperties);

            \Log::info("VM/Container reinstalled successfully", [
                'service_id' => $service->id,
                'hostname' => $hostname
            ]);

        } catch (\Exception $e) {
            \Log::error("Failed to reinstall VM/Container", [
                'service_id' => $service->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }


    public function startVm(Service $service, $settings, $properties): void
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        $vmid = $properties['vmid'];

        try {
            $response = $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $vmid . '/status/start', 'POST');

            // Wait for task completion if UPID is returned
            if (isset($response['data']) && is_string($response['data'])) {
                $taskResult = $this->waitForTask($response['data']);
                if (!$taskResult) {
                    // Check if the error is because VM is already running
                    $taskStatus = $this->getTaskStatus($response['data']);
                    if (isset($taskStatus['exitstatus']) && $taskStatus['exitstatus'] !== 'OK') {
                        $errorMessage = $taskStatus['status'] ?? 'Unknown error';
                        if (strpos($errorMessage, 'already running') !== false) {
                            \Log::info("VM/Container is already running", [
                                'vmid' => $vmid,
                                'vm_type' => $vmType
                            ]);
                            return; // Already running, no error
                        }
                    }
                    //throw new \Exception('Start task failed');
                }
            }

            \Log::info("VM/Container started successfully", [
                'vmid' => $vmid,
                'vm_type' => $vmType,
                'service_id' => $service->id
            ]);

        } catch (RequestException $e) {
            // Proxmox may return a 500 error if the VM is already running.
            if ($e->response->status() === 500) {
                $errorBody = $e->response->json();
                if (isset($errorBody['errors']) && strpos(json_encode($errorBody['errors']), 'already running') !== false) {
                    \Log::info("VM/Container is already running", [
                        'vmid' => $vmid,
                        'vm_type' => $vmType
                    ]);
                    return; // Already running, no error
                }
            }

            \Log::error("Failed to start VM/Container", [
                'vmid' => $vmid,
                'vm_type' => $vmType,
                'error' => $e->getMessage()
            ]);

            throw new \Exception('Failed to start VM/Container: ' . $e->getMessage());
        } catch (\Exception $e) {
            // Check if error message indicates VM is already running
            if (strpos($e->getMessage(), 'already running') !== false) {
                \Log::info("VM/Container is already running", [
                    'vmid' => $vmid,
                    'vm_type' => $vmType
                ]);
                return; // Already running, no error
            }

            \Log::error("Failed to start VM/Container", [
                'vmid' => $vmid,
                'vm_type' => $vmType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    public function stopVm(Service $service, $settings, $properties): void
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        $vmid = $properties['vmid'];

        try {
            $response = $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $vmid . '/status/stop', 'POST');

            // Wait for task completion if UPID is returned
            if (isset($response['data']) && is_string($response['data'])) {
                if (!$this->waitForTask($response['data'])) {
                    throw new \Exception('Stop task failed');
                }
            }

            \Log::info("VM/Container stopped successfully", [
                'vmid' => $vmid,
                'vm_type' => $vmType,
                'service_id' => $service->id
            ]);

        } catch (RequestException $e) {
            // Proxmox may return a 500 error if the VM is already stopped.
            if ($e->response->status() === 500) {
                \Log::info("VM/Container is already stopped", [
                    'vmid' => $vmid,
                    'vm_type' => $vmType
                ]);
                return; // Already stopped, no error
            }

            \Log::error("Failed to stop VM/Container", [
                'vmid' => $vmid,
                'vm_type' => $vmType,
                'error' => $e->getMessage()
            ]);

            throw new \Exception('Failed to stop VM/Container: ' . $e->getMessage());
        } catch (\Exception $e) {
            \Log::error("Failed to stop VM/Container", [
                'vmid' => $vmid,
                'vm_type' => $vmType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    public function restartVm(Service $service, $settings, $properties): void
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        $vmid = $properties['vmid'];

        try {
            $response = $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $vmid . '/status/reboot', 'POST');

        } catch (RequestException $e) {
            \Log::error("Failed to restart VM/Container", [
                'vmid' => $vmid,
                'vm_type' => $vmType,
                'error' => $e->getMessage()
            ]);

            throw new \Exception('Failed to restart VM/Container: ' . $e->getMessage());
        } catch (\Exception $e) {
            \Log::error("Failed to restart VM/Container", [
                'vmid' => $vmid,
                'vm_type' => $vmType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }



    public function upgradeServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        $configData = [
            'cores' => $settings['cores'],
            'memory' => $settings['memory'],
        ];
        
        $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/config', 'PUT', $configData);
        
        return true;
    }

    /**
     * Get resource usage statistics
     */
    public function getResourceUsage(Service $service, $settings, $properties): array
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';

        try {
            $response = $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/current');

            return [
                'cpu_usage' => round(($response['data']['cpu'] ?? 0) * 100, 2),
                'memory_usage' => $response['data']['mem'] ?? 0,
                'memory_total' => $response['data']['maxmem'] ?? 0,
                'memory_percentage' => $response['data']['maxmem'] > 0 ? round(($response['data']['mem'] / $response['data']['maxmem']) * 100, 2) : 0,
                'disk_usage' => $response['data']['disk'] ?? 0,
                'disk_total' => $response['data']['maxdisk'] ?? 0,
                'disk_percentage' => $response['data']['maxdisk'] > 0 ? round(($response['data']['disk'] / $response['data']['maxdisk']) * 100, 2) : 0,
                'uptime' => $response['data']['uptime'] ?? 0,
                'status' => $response['data']['status'] ?? 'unknown',
                'network_in' => $response['data']['netin'] ?? 0,
                'network_out' => $response['data']['netout'] ?? 0,
            ];
        } catch (\Exception $e) {
            $this->handleProxmoxError($e, 'resource monitoring', ['vmid' => $properties['vmid']]);
        }
    }

    /**
     * Get current disk size
     */
    private function getCurrentDiskSize($vmid, $vmType): int
    {
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        $response = $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $vmid . '/config');

        if ($vmType === 'qemu') {
            // For QEMU, check scsi0 disk
            if (isset($response['data']['scsi0'])) {
                preg_match('/size=(\d+)G/', $response['data']['scsi0'], $matches);
                return isset($matches[1]) ? (int)$matches[1] : 0;
            }
        } else {
            // For LXC, check rootfs
            if (isset($response['data']['rootfs'])) {
                preg_match('/size=(\d+)G/', $response['data']['rootfs'], $matches);
                return isset($matches[1]) ? (int)$matches[1] : 0;
            }
        }

        return 0;
    }

    /**
     * Verify disk resize was successful
     */
    private function verifyDiskResize($vmid, $vmType, $expectedSize): bool
    {
        $maxAttempts = 10;
        $attempts = 0;

        while ($attempts < $maxAttempts) {
            $currentSize = $this->getCurrentDiskSize($vmid, $vmType);

            if ($currentSize >= $expectedSize) {
                return true;
            }

            sleep(2);
            $attempts++;
        }

        return false;
    }

    /**
     * Resize disk storage with retry mechanism
     */
    public function resizeDisk(Service $service, $settings, $properties, int $newSizeGB): bool
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        $vmType = $properties['vm_type'] ?? 'qemu';
        $vmid = $properties['vmid'];

        // Get current disk size
        $currentSize = $this->getCurrentDiskSize($vmid, $vmType);

        if ($newSizeGB <= $currentSize) {
            throw new \Exception("New size ({$newSizeGB}GB) must be larger than current size ({$currentSize}GB)");
        }

        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                \Log::info("Attempting disk resize", [
                    'vmid' => $vmid,
                    'vm_type' => $vmType,
                    'current_size' => $currentSize,
                    'new_size' => $newSizeGB,
                    'attempt' => $retryCount + 1
                ]);

                if ($vmType === 'qemu') {
                    // Use bash script approach: set disk size directly in config
                    $storage = $this->config('storage');
                    $response = $this->request('/nodes/' . $this->config('node') . '/qemu/' . $vmid . '/config', 'PUT', [
                        'scsi0' => $storage . ':' . $newSizeGB
                    ]);
                } else {
                    // For LXC, use absolute size
                    $response = $this->request('/nodes/' . $this->config('node') . '/lxc/' . $vmid . '/resize', 'PUT', [
                        'disk' => 'rootfs',
                        'size' => $newSizeGB . 'G'
                    ]);
                }

                // Wait for task completion with extended timeout for large resizes
                if (isset($response['data']) && is_string($response['data'])) {
                    $timeout = max(300, ($newSizeGB - $currentSize) * 30); // Minimum 5 min, +30s per GB
                    if (!$this->waitForTask($response['data'], $timeout)) {
                        throw new \Exception('Resize task failed or timed out');
                    }
                }

                // Verify the resize was successful
                if ($this->verifyDiskResize($vmid, $vmType, $newSizeGB)) {
                    \Log::info("Disk resize successful", [
                        'vmid' => $vmid,
                        'vm_type' => $vmType,
                        'new_size' => $newSizeGB,
                        'attempts' => $retryCount + 1
                    ]);
                    return true;
                }

                throw new \Exception('Disk resize verification failed');

            } catch (\Exception $e) {
                $retryCount++;

                \Log::warning("Disk resize attempt failed", [
                    'vmid' => $vmid,
                    'vm_type' => $vmType,
                    'new_size' => $newSizeGB,
                    'attempt' => $retryCount,
                    'error' => $e->getMessage()
                ]);

                if ($retryCount >= $maxRetries) {
                    $this->handleProxmoxError($e, 'disk resize', [
                        'vmid' => $vmid,
                        'new_size' => $newSizeGB,
                        'attempts' => $retryCount
                    ]);
                    return false;
                }

                // Wait before retry
                sleep(5);
            }
        }

        return false;
    }

    /**
     * Handle console view
     */
    public function console(Service $service, $settings, $properties): array
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        $vmid = $properties['vmid'];
        $vmType = $properties['vm_type'] ?? 'qemu';
        $vmStatus = $this->getVmStatus($service, $settings, $properties);

        // Generate console URL
        $consoleUrl = 'https://' . $this->config('ip') . ':' . $this->config('port') .
                     '/#v1:0:18:4::::::' . $vmid . ':4:' . ($vmType === 'qemu' ? 'kvm' : 'lxc') . ':' . $this->config('node');

        return [
            'vmid' => $vmid,
            'vm_type' => $vmType,
            'server_status' => $vmStatus,
            'console_url' => $consoleUrl,
            'ip_address' => $properties['allocated_ip'] ?? null,
            'hostname' => $properties['hostname'] ?? null,
            'cores' => $settings['cores'] ?? null,
            'memory' => $settings['memory'] ?? null,
            'disk_size' => $settings['disk_size'] ?? null,
            'network_type' => $settings['network_type'] ?? 'public',
        ];
    }

    /**
     * Handle reinstall view
     */
    public function reinstall(Service $service, $settings, $properties): array
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        $vmid = $properties['vmid'];
        $vmType = $properties['vm_type'] ?? 'qemu';
        $vmStatus = $this->getVmStatus($service, $settings, $properties);

        // Get available templates
        $templates = [];
        try {
            if ($vmType === 'qemu') {
                $response = $this->request('/nodes/' . $this->config('node') . '/qemu');
                $templateList = [];

                foreach ($response['data'] as $vm) {
                    if (isset($vm['template']) && $vm['template'] == 1) {
                        $templateList[] = [
                            'vmid' => $vm['vmid'],
                            'name' => $vm['name']
                        ];
                    }
                }

                usort($templateList, function($a, $b) {
                    return strcmp($a['name'], $b['name']);
                });

                foreach ($templateList as $template) {
                    $templates[$template['vmid']] = $template['name'];
                }
            } else {
                $storage = $this->config('lxc_storage') ?: $this->config('storage');
                $response = $this->request('/nodes/' . $this->config('node') . '/storage/' . $storage . '/content');
                foreach ($response['data'] as $template) {
                    if ($template['content'] === 'vztmpl') {
                        $templateName = basename($template['volid']);
                        $displayName = $this->formatLxcTemplateName($templateName);
                        $templates[$template['volid']] = $displayName;
                    }
                }
            }
        } catch (\Exception $e) {
            $templates = [];
        }

        return [
            'vmid' => $vmid,
            'vm_type' => $vmType,
            'server_status' => $vmStatus,
            'templates' => $templates,
            'current_template' => $properties['template'] ?? null,
            'current_ssh_key' => $properties['ssh_key'] ?? null,
            'ip_address' => $properties['allocated_ip'] ?? null,
            'hostname' => $properties['hostname'] ?? null,
            'cores' => $settings['cores'] ?? null,
            'memory' => $settings['memory'] ?? null,
            'disk_size' => $settings['disk_size'] ?? null,
        ];
    }

    /**
     * Handle reinstall action
     */
    public function reinstallServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }

        $config = request()->all();

        // Validate required fields
        if (empty($config['template'])) {
            throw new \Exception('Template is required for reinstall');
        }

        if (!isset($config['confirm_reinstall'])) {
            throw new \Exception('Reinstall confirmation is required');
        }

        $vmid = $properties['vmid'];
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';

        try {
            \Log::info("Starting VM/Container reinstallation", [
                'vmid' => $vmid,
                'service_id' => $service->id,
                'new_template' => $config['template']
            ]);

            // Step 1: Stop and delete existing VM
            try {
                $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $vmid . '/status/stop', 'POST');
                sleep(5);
            } catch (\Exception $e) {
                // Continue if stop fails
            }

            $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $vmid, 'DELETE');
            sleep(5);

            // Step 2: Create new VM with same VMID and settings
            $newConfig = array_merge($settings, [
                'template' => $config['template'],
                'password' => $config['password'] ?: Str::random(12),
                'ssh_key' => $config['ssh_key'] ?? null,
                'hostname' => $properties['hostname'] ?? 'vm-' . $service->id,
                'vm_type' => $vmType,
                'network_type' => $settings['network_type'] ?? 'public',
            ]);

            // Use existing IP if available
            $allocatedIp = $properties['allocated_ip'] ?? $this->findAvailableIp($newConfig['network_type']);
            if (!$allocatedIp) {
                throw new \Exception('No available IP addresses');
            }

            $networkType = $newConfig['network_type'];
            $bridge = $networkType === 'public' ? $this->config('public_bridge') : $this->config('private_bridge');
            $subnet = $networkType === 'public' ? $this->config('public_subnet') : $this->config('private_subnet');
            $gateway = $networkType === 'public' ? $this->config('public_gateway') : $this->config('private_gateway');

            if ($vmType === 'qemu') {
                // Clone template
                $cloneData = [
                    'newid' => $vmid,
                    'name' => $newConfig['hostname'],
                    'full' => 1,
                    'target' => $this->config('node'),
                ];

                $response = $this->request('/nodes/' . $this->config('node') . '/qemu/' . $newConfig['template'] . '/clone', 'POST', $cloneData);

                if (!$this->waitForTask($response['data'])) {
                    throw new \Exception('Failed to clone VM template');
                }

                // Configure VM
                $configData = [
                    'cores' => $newConfig['cores'],
                    'memory' => $newConfig['memory'],
                    'ciuser' => 'root',
                    'ipconfig0' => "ip={$allocatedIp}/{$subnet},gw={$gateway}",
                    'net0' => "virtio,bridge={$bridge}",
                ];

                if ($newConfig['password']) {
                    $configData['cipassword'] = $newConfig['password'];
                }

                if (!empty($newConfig['ssh_key'])) {
                    $configData['sshkeys'] = urlencode($newConfig['ssh_key']);
                }

                $this->request('/nodes/' . $this->config('node') . '/qemu/' . $vmid . '/config', 'PUT', $configData);

                // Start VM
                $this->request('/nodes/' . $this->config('node') . '/qemu/' . $vmid . '/status/start', 'POST');
            } else {
                // Create LXC
                $storage = $this->config('storage');
                $rootfs = $storage . ':' . $newConfig['disk_size'];

                $createData = [
                    'vmid' => $vmid,
                    'hostname' => $newConfig['hostname'],
                    'cores' => $newConfig['cores'],
                    'memory' => $newConfig['memory'],
                    'rootfs' => $rootfs,
                    'net0' => "name=eth0,bridge={$bridge},ip={$allocatedIp}/{$subnet},gw={$gateway}",
                    'ostemplate' => $newConfig['template'],
                    'unprivileged' => 1,
                ];

                if ($newConfig['password']) {
                    $createData['password'] = $newConfig['password'];
                }

                if (!empty($newConfig['ssh_key'])) {
                    $createData['ssh-public-keys'] = $newConfig['ssh_key'];
                }

                $response = $this->request('/nodes/' . $this->config('node') . '/lxc', 'POST', $createData);

                if (!$this->waitForTask($response['data'])) {
                    throw new \Exception('Failed to create LXC container');
                }

                // Start container
                $this->request('/nodes/' . $this->config('node') . '/lxc/' . $vmid . '/status/start', 'POST');
            }

            // Update service properties
            $service->properties()->updateOrCreate(['key' => 'template'], [
                'name' => 'Template',
                'value' => $newConfig['template'],
            ]);

            $service->properties()->updateOrCreate(['key' => 'allocated_ip'], [
                'name' => 'Allocated IP',
                'value' => $allocatedIp,
            ]);

            if (!empty($newConfig['ssh_key'])) {
                $service->properties()->updateOrCreate(['key' => 'ssh_key'], [
                    'name' => 'SSH Key',
                    'value' => $newConfig['ssh_key'],
                ]);
            } else {
                $service->properties()->where('key', 'ssh_key')->delete();
            }

            \Log::info("VM/Container reinstalled successfully", [
                'service_id' => $service->id,
                'vmid' => $vmid,
                'new_template' => $newConfig['template']
            ]);

            return [
                'vmid' => $vmid,
                'ip' => $allocatedIp,
                'password' => $newConfig['password'],
                'template' => $newConfig['template'],
            ];

        } catch (\Exception $e) {
            \Log::error("Failed to reinstall VM/Container", [
                'service_id' => $service->id,
                'vmid' => $vmid,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }
}
