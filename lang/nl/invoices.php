<?php

return [
    'invoices' => 'Facturen',
    'product' => 'Product',
    'price' => 'Prijs',
    'status' => 'Status',
    'actions' => 'Acties',
    'view' => 'Weergeven',
    'id' => 'ID',
    'total' => 'Totaal',
    'subtotal' => 'Subtotaal',
    'invoice' => 'Factuur #:id',
    'unit_price' => 'Prijs per stuk',
    'bill_to' => 'Factuur aan',
    'paid' => 'Betaald',
    'payment_pending' => 'Betaling in afwachting',
    'checking_payment' => 'Betaling controleren',
    'invoice_date' => 'Factuurdatum',
    'invoice_no' => 'Factuurnummer',
    'tax_id' => 'BTW nummer',
    'company_id' => 'KVK nummer',
    'item' => 'Artikel',
    'quantity' => 'Aantal',
    'transactions' => 'Transacties',
    'transaction_id' => 'Transactie-ID',
    'gateway' => 'Gateway',
    'payment_date' => 'Betaaldatum',
    'payment_method' => 'Betaalmethode',
    'amount' => 'Bedrag',
    'date' => 'Datum',
];
