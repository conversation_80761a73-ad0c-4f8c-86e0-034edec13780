<?php

return [
    'invoices' => 'Facturas',
    'product' => 'Producto',
    'price' => 'Precio',
    'status' => 'Estado',
    'actions' => 'Acciones',
    'view' => 'Ver',
    'id' => 'ID',
    'total' => 'Total',
    'subtotal' => 'Subtotal',
    'invoice' => 'Factura #:id',
    'unit_price' => 'Precio por unidad',
    'bill_to' => 'Facturar a',
    'paid' => 'Pagado',
    'payment_pending' => 'Pago pendiente',
    'checking_payment' => 'Comprobando pago',
    'invoice_date' => 'Fecha de factura',
    'invoice_no' => 'Número de factura',
    'tax_id' => 'ID del impuesto',
    'company_id' => 'ID de la compañía',
    'item' => 'Artículo',
    'quantity' => 'Cantidad',
    'transactions' => 'Transacciones',
    'transaction_id' => 'Núm. de la transacción',
    'gateway' => 'Pasarela de pago',
    'payment_date' => 'Fecha de pago',
    'payment_method' => 'Método Pago',
    'amount' => 'Cantidad',
    'date' => 'Fecha',
];
