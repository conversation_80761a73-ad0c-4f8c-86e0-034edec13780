<?php

return [
    'invoices' => 'Faturas',
    'product' => 'Produto',
    'price' => 'Preço',
    'status' => 'Situação',
    'actions' => 'Ações',
    'view' => 'Visualizar',
    'id' => 'ID',
    'total' => 'Total',
    'subtotal' => 'Subtotal',
    'invoice' => 'Fatura #:id',
    'unit_price' => 'Preço unitário',
    'bill_to' => 'Cobrar para',
    'paid' => 'Pago',
    'payment_pending' => 'Pagamento pendente',
    'checking_payment' => 'Verificando pagamento',
    'invoice_date' => 'Data da fatura',
    'invoice_no' => 'Fatura N.º',
    'tax_id' => 'Tax ID',
    'company_id' => 'Company ID',
    'item' => 'Item',
    'quantity' => 'Quantidade',
    'transactions' => 'Transações',
    'transaction_id' => 'ID da transação',
    'gateway' => 'Gateway',
    'payment_date' => 'Data de pagamento',
    'payment_method' => 'Forma de pagamento',
    'amount' => 'Quantidade',
    'date' => 'Data',
];
