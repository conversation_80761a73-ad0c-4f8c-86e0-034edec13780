/* Tailwind CSS compiled styles */
@import url('https://cdn.tailwindcss.com/3.3.6/tailwind.min.css');

/* Custom font family */
.font-inter {
    font-family: 'Inter', sans-serif;
}

/* Material Design inspired components */
/* Material Card */
.material-card {
    background-color: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: box-shadow 0.2s ease;
}

.material-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.material-card-elevated {
    background-color: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease;
}

.material-card-elevated:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Material Button */
.material-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    outline: none;
    border: none;
    cursor: pointer;
    text-decoration: none;
}

.material-btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.material-btn-primary {
    background-color: #2563eb;
    color: #ffffff;
}

.material-btn-primary:hover {
    background-color: #1d4ed8;
}

.material-btn-secondary {
    background-color: #f3f4f6;
    color: #111827;
}

.material-btn-secondary:hover {
    background-color: #e5e7eb;
}

.material-btn-success {
    background-color: #059669;
    color: #ffffff;
}

.material-btn-success:hover {
    background-color: #047857;
}

.material-btn-danger {
    background-color: #dc2626;
    color: #ffffff;
}

.material-btn-danger:hover {
    background-color: #b91c1c;
}

.material-btn-warning {
    background-color: #d97706;
    color: #ffffff;
}

.material-btn-warning:hover {
    background-color: #b45309;
}
/* Material Input */
.material-input {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    outline: none;
}

.material-input::placeholder {
    color: #9ca3af;
}

.material-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.material-input-error {
    border-color: #fca5a5;
}

.material-input-error:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* Material Select */
.material-select {
    display: block;
    width: 100%;
    padding: 0.5rem 2.5rem 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    appearance: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    outline: none;
}

.material-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Material Checkbox */
.material-checkbox {
    height: 1rem;
    width: 1rem;
    color: #2563eb;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.material-checkbox:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Material Radio */
.material-radio {
    height: 1rem;
    width: 1rem;
    color: #2563eb;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
}

.material-radio:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}
/* Material Badge */
.material-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.material-badge-primary {
    background-color: #dbeafe;
    color: #1e40af;
}

.material-badge-success {
    background-color: #dcfce7;
    color: #166534;
}

.material-badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.material-badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.material-badge-gray {
    background-color: #f3f4f6;
    color: #374151;
}
/* Material Alert */
.material-alert {
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid;
}

.material-alert-success {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.material-alert-error {
    background-color: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.material-alert-warning {
    background-color: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

.material-alert-info {
    background-color: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}
    
    /* Material Table */
    .material-table {
        @apply min-w-full divide-y divide-gray-200;
    }
    
    .material-table thead {
        @apply bg-gray-50;
    }
    
    .material-table th {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }
    
    .material-table td {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }
    
    .material-table tbody tr {
        @apply hover:bg-gray-50 transition-colors duration-150;
    }
    
    /* Material Navigation */
    .material-nav-item {
        @apply flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200;
    }
    
    .material-nav-item.active {
        @apply bg-blue-50 text-blue-600 border-r-2 border-blue-600;
    }
    
    /* Material Dropdown */
    .material-dropdown {
        @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50 border border-gray-200;
    }
    
    .material-dropdown-item {
        @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150;
    }
    
    /* Material Modal */
    .material-modal-overlay {
        @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
    }
    
    .material-modal {
        @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white;
    }
    
    /* Material Progress */
    .material-progress {
        @apply w-full bg-gray-200 rounded-full h-2;
    }
    
    .material-progress-bar {
        @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
    }
    
    /* Material Skeleton */
    .material-skeleton {
        @apply animate-pulse bg-gray-200 rounded;
    }
    
    /* Material Divider */
    .material-divider {
        @apply border-t border-gray-200 my-4;
    }
    
    /* Material Icon Button */
    .material-icon-btn {
        @apply p-2 rounded-full hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
    }
    
    /* Material Floating Action Button */
    .material-fab {
        @apply fixed bottom-6 right-6 w-14 h-14 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-200 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
    }
}

/* Custom animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out;
}

/* Responsive utilities */
@media (max-width: 768px) {
    .material-card {
        @apply mx-4;
    }
    
    .material-table {
        @apply text-sm;
    }
    
    .material-table th,
    .material-table td {
        @apply px-3 py-2;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .dark-mode .material-card {
        @apply bg-gray-800 border-gray-700;
    }
    
    .dark-mode .material-input {
        @apply bg-gray-800 border-gray-600 text-white;
    }
    
    .dark-mode .material-nav-item {
        @apply text-gray-300 hover:bg-gray-700 hover:text-white;
    }
}
