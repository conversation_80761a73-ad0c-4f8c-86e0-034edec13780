document.addEventListener("livewire:init",()=>{Livewire.hook("request",({fail:i})=>{i(({status:e,preventDefault:t})=>{e===419&&(window.location.reload(),t())})})});Alpine.store("notifications",{init(){Livewire.on("notify",i=>{Alpine.store("notifications").addNotification(i)})},notifications:[],addNotification(i){i=i[0],i.show=!1,i.id=Date.now()+Math.floor(Math.random()*1e3),this.notifications.push(i),Alpine.nextTick(()=>{this.notifications=this.notifications.map(e=>(e.id===e.id&&(e.show=!0),e))}),setTimeout(()=>{this.removeNotification(i.id)},i.timeout||5e3)},removeNotification(i){this.notifications=this.notifications.map(e=>(e.id===i&&(e.show=!1),e)).filter(e=>e.show)}});
