version: '3.8'
x-common:
  database:
    &db-environment
    # Do not remove the "&db-password" from the end of the line below, it is important
    # for Paymenter functionality.
    MYSQL_PASSWORD: &db-password "CHANGE_ME"
    MYSQL_ROOT_PASSWORD: "CHANGE_ME_TOO"

#
# ------------------------------------------------------------------------------------------
# DANGER ZONE BELOW
#
# The remainder of this file likely does not need to be changed. Please only make modifications
# below if you understand what you are doing.
#
services:
  database:
    image: mariadb:10.11
    restart: always
    command: --default-authentication-plugin=mysql_native_password
    volumes:
      - "./database:/var/lib/mysql"
    environment:
      <<: *db-environment
      MYSQL_DATABASE: "paymenter"
      MYSQL_USER: "paymenter"
  cache:
    image: redis:alpine
    restart: always
  paymenter:
    image: ghcr.io/paymenter/paymenter:master
    restart: always
    ports:
      - "80:80"
    links:
      - database
      - cache
    volumes:
      - "./:/app/var/"
      - "./storage/logs:/app/storage/logs"
      - "./storage/public:/app/storage/public"
    environment:
      DB_PASSWORD: *db-password
      APP_ENV: "production"
      CACHE_STORE: "redis"
      REDIS_HOST: "cache"
      DB_CONNECTION: "mariadb"
      DB_HOST: "database"
      DB_PORT: "3306"
networks:
  default:
    ipam:
      config:
        - subnet: **********/16