<br>
<p align="center">
  <a href="https://paymenter.org">
    <picture>
      <source media="(max-width: 768px)" srcset="https://paymenter.org/iso.svg" width="65px">
      <source media="(prefers-color-scheme: dark)" srcset="https://paymenter.org/iso.svg" width="80px">
      <source media="(prefers-color-scheme: light)" srcset="https://paymenter.org/iso.svg" width="80px">
      <img alt="Paymenter Isotype" src="https://paymenter.org/iso.svg">
    </picture>
  </a>
</p>
<h1 align="center">
  Paymenter
</h1>

<div align="center">
  <h3>Open-Source Billing, Built for Hosting</h3>
  <p>Automate subscriptions, eliminate billing chaos, and grow your hosting business – without vendor lock-ins or hidden costs.</p>
</div>

<h4 align="center">
  <a href="https://paymenter.org">Website</a> ·
  <a href="https://paymenter.org/docs/installation/install">Documentation</a> ·
  <a href="https://demo.paymenter.org">Live Demo</a>
</h4>

 <div align="center">
   
  [![License](https://img.shields.io/badge/license-MIT-blue.svg)](https://github.com/Paymenter/paymenter/blob/master/LICENSE)
  [![Downloads](https://img.shields.io/github/downloads/paymenter/paymenter/total)]()
  [![GitHub release (latest by date)](https://img.shields.io/github/v/release/paymenter/paymenter)](https://github.com/Paymenter/paymenter/releases)
    <br>
    <br>
  [![Discord](https://img.shields.io/discord/882318291014651924?logo=discord&labelColor=white&color=5865f2)](https://discord.gg/xB4UUT3XQg)
  
</div>

<div align="center">
    <picture>
      <source media="(max-width: 768px)" srcset="https://upload.wikimedia.org/wikipedia/commons/c/ca/1x1.png">
      <source media="(prefers-color-scheme: dark)" srcset="https://paymenter.org/landing/screenshots/dark/dashboard.webp">
      <source media="(prefers-color-scheme: light)" srcset="https://paymenter.org/landing/screenshots/light/dashboard.webp">
      <img alt="Paymenter Dashboard" src="https://paymenter.org/landing/screenshots/dark/dashboard.webp">
    </picture>
    <br>
</div>

## Getting Started

#### Installation & Documentation

For a detailed explanation of how to install and configure Paymenter, take a look at our [documentation here](https://paymenter.org/docs/getting-started/introduction/).

Or, get additional help via [Community Discord](https://discord.gg/xB4UUT3XQg).

#### Requirements

The requirements for Paymenter are the following:

- PHP (8.2 or higher).
- Composer.

## What is Paymenter?

Paymenter is an open-source billing platform tailored for hosting companies. It simplifies the management of hosting services, providing a seamless experience for both providers and customers. Built on modern web technologies, Paymenter offers a flexible and robust solution for your hosting business needs.

### Key Features:
- User-Friendly Interface: Paymenter is designed with simplicity in mind, ensuring an intuitive experience for users of all technical levels.
- Open Source and Extensible: As an open-source platform, Paymenter encourages community contributions and customization. Its architecture allows for extensive modifications and integration with other tools.
- Efficient Management: Streamline your operations with Paymenter powerful admin panel, designed to enhance productivity and reduce overhead.
- Secure and Reliable: Built with security as a priority, Paymenter ensures the protection of your data and transactions.
- Community Driven: Join an engaged community of developers and hosting providers to collaborate and drive the future development of Paymenter.

Paymenter is available under the MIT license, offering you the freedom to adapt and evolve the platform to meet your specific requirements.

## Sponsors

Thanks to all sponsors for helping fund Paymenter's development. [Interested in becoming a sponsor?](https://github.com/sponsors/Paymenter)

## License

Licensed under the [MIT License](https://github.com/Paymenter/Paymenter/blob/master/LICENSE).
