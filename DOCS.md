# Dokumentasi Paymenter Development

## Daftar Isi
1. [Extensions](#extensions)
   - [Pengenalan](#pengenalan)
   - [Membuat Extension](#membuat-extension)
   - [Admin Pages](#admin-pages)
   - [Konfigurasi](#konfigurasi)
   - [Lifecycle Hooks](#lifecycle-hooks)
   - [Routes dan Views](#routes-dan-views)
   - [Gateway Extensions](#gateway-extensions)
   - [Server Extensions](#server-extensions)
2. [Themes](#themes)
   - [Membuat Theme](#membuat-theme)
   - [Struktur Folder](#struktur-folder)
   - [Building Assets](#building-assets)

---

## Extensions

### Pengenalan

Extensions adalah cara yang powerful untuk memperluas fungsionalitas sistem Paymenter. Anda dapat membuat extensions untuk menambahkan fitur-fitur baru ke dalam sistem.

### Membuat Extension

Untuk membuat extension baru, jalankan perintah berikut:

```bash
php artisan app:extension:create
```

Jika Anda perlu menonaktifkan extension dan tidak dapat melakukannya dari admin panel, jalankan perintah:

```bash
php artisan app:extension:disable
```

### Admin Pages

Menambahkan halaman admin baru ke Paymenter sangat mudah berkat framework Filament yang versatile.

- **Membuat Admin Page**: Pelajari cara membuat halaman admin baru dengan mengikuti [dokumentasi Filament](https://filamentphp.com/docs/3.x/panels/pages)
- **Membuat Resources**: Untuk mengontrol model, Anda dapat membuat resources dengan mengikuti [dokumentasi Filament Resources](https://filamentphp.com/docs/3.x/panels/resources/getting-started)

**Catatan**: Pastikan untuk memilih path extension Anda saat diminta.

### Konfigurasi

Setiap extension dapat memiliki konfigurasi sendiri melalui function `getConfig`.

#### Contoh Konfigurasi:

```php
public function getConfig($values = [])
{
    return [
        [
            'name' => 'host',
            'label' => 'Pterodactyl URL',
            'type' => 'text',
            'default' => 'https://example.com/',
            'description' => 'Pterodactyl URL',
            'required' => true,
            'validation' => 'url',
        ],
        [
            'name' => 'location',
            'label' => 'Location',
            'type' => 'select',
            'default' => '1',
            'description' => 'Location your node is in?',
            'required' => true,
            'options' => [
                '1' => 'Location 1',
                '2' => 'Location 2',
            ],
        ]
    ];
}
```

#### Parameter Konfigurasi yang Didukung:

| Name | Description | Type | Required |
|------|-------------|------|----------|
| name | Nama field yang akan digunakan untuk mendapatkan nilai field | string | true |
| label | Label field yang akan ditampilkan | string | true |
| type | Tipe field: select, tags, text, textarea, markdown, password, email, number, color, file, checkbox, placeholder | string | true |
| default | Nilai default field | string | false |
| description | Deskripsi field | string | false |
| required | Apakah field wajib diisi | boolean | false |
| validation | Laravel validation rules | string | false |
| options | Opsi untuk field select (array dengan key-value) | array | false |
| disabled | Apakah field dinonaktifkan | boolean | false |

**Catatan**: `getCheckoutConfig` hanya mendukung tipe: select, radio, text, password, number, dan checkbox.

### Lifecycle Hooks

Hooks berikut dapat digunakan untuk menjalankan kode pada tahap yang berbeda dari lifecycle extension:

- **boot**: Dijalankan saat extension di-boot (hanya ketika extension diaktifkan), misalnya untuk registrasi routes
- **enabled**: Dijalankan saat extension diaktifkan, misalnya untuk menjalankan migrasi
- **disabled**: Dijalankan saat extension dinonaktifkan, misalnya untuk menghapus migrasi
- **updated**: Dijalankan saat extension diupdate, misalnya untuk membuat remote hooks

### Routes dan Views

Anda dapat mendaftarkan routes dan views dalam function `boot` extension.

#### Contoh:

```php
use App\Helpers\ExtensionHelper;
use Paymenter\Extensions\Others\Example\Middleware\ExampleMiddleware;

public function boot()
{
    // Register routes
    require __DIR__ . '/routes/web.php';
    
    // Register views
    View::addNamespace('extension', __DIR__ . '/resources/views');
    
    // Register middleware (web digunakan untuk semua routes)
    ExtensionHelper::registerMiddleware('web', ExampleMiddleware::class);
}
```

---

## Gateway Extensions

Gateway extensions digunakan untuk memproses pembayaran dan menambahkan metode pembayaran baru ke sistem.

### Hooks yang Tersedia:

#### `canUseGateway`
Digunakan untuk mengecek apakah gateway dapat digunakan:

```php
public function canUseGateway($items, $type)
{
    // Cek apakah user dapat menggunakan gateway
    return true;
}
```

- `$items`: List dari `InvoiceItem` atau array dengan `Product`, `Price`, dan `Quantity`
- `$type`: Either `invoice` atau `cart`

#### `pay`
Digunakan untuk memproses pembayaran:

```php
public function pay(Invoice $invoice, $total)
{
    // Proses pembayaran
}
```

- `$invoice`: Invoice yang sedang dibayar
- `$total`: Total amount yang perlu dibayar

---

## Server Extensions

Server extensions digunakan untuk membuat server di panel seperti Pterodactyl.

### Hooks yang Tersedia:

#### `getProductConfig`
Ditampilkan kepada admin saat mengkonfigurasi produk:

```php
public function getProductConfig($values = [])
{
    return [
        [
            'name' => 'ram',
            'label' => 'Ram',
            'type' => 'number',
            'required' => true,
            'default' => 1024,
            'description' => 'The amount of ram in MB',
        ],
    ];
}
```

#### `getCheckoutConfig`
Ditampilkan kepada user saat checkout:

```php
use App\Models\Product;

public function getCheckoutConfig(Product $product, $values = [])
{
    return [
        [
            'name' => 'location',
            'label' => 'Location',
            'type' => 'select',
            'required' => true,
            'options' => [
                '1' => 'Location 1',
                '2' => 'Location 2',
            ],
        ]
    ];
}
```

#### Server Management Hooks
Hooks untuk mengelola server dengan 3 parameter:
- `Service $service`: Service yang sedang diproses
- `$settings`: Settings dari product `getProductConfig`
- `$properties`: Custom properties dan values dari `getCheckoutConfig`

```php
public function createServer(Service $service, $settings, $properties) {
    // Buat server
}

public function suspendServer(Service $service, $settings, $properties) {
    // Suspend server
}

public function unsuspendServer(Service $service, $settings, $properties) {
    // Unsuspend server
}

public function terminateServer(Service $service, $settings, $properties) {
    // Terminate server
}

public function upgradeServer(Service $service, $settings, $properties) {
    // Upgrade server
}
```

#### `getActions`
Menambahkan actions ke service show page:

```php
public function getActions(Service $service)
{
    return [
        [
            'name' => 'control_panel',
            'label' => 'Go to control panel',
            'url' => 'https://panel.paymenter.org',
            'type' => 'button',
        ],
        [
            'name' => 'console',
            'label' => 'Go to console',
            'type' => 'view',
        ],
    ];
}
```

Untuk URL yang time-sensitive, gunakan function:

```php
public function getActions(Service $service)
{
    return [
        [
            'name' => 'control_panel',
            'label' => 'Go to control panel',
            'function' => 'getControlPanelUrl',
            'type' => 'button',
        ],
        [
            'name' => 'console',
            'label' => 'Go to console',
            'type' => 'view',
            'function' => 'getView',
        ],
    ];
}

public function getControlPanelUrl(Service $service)
{
    return 'https://panel.paymenter.org/' . $service->id;
}

public function getView(Service $service, $settings, $properties, $view)
{
    return view('extension::' . $view, ['service' => $service]);
}
```

---

## Themes

### Prerequisites

Sebelum membuat theme, pastikan Anda memiliki:
- Instalasi Paymenter yang berfungsi
- Pengetahuan HTML, CSS
- Node.js dan NPM terinstal di sistem Anda

### Membuat Theme

Anda dapat membuat theme dengan cara:

1. **Copy folder default**: Salin folder default yang sudah ada
2. **Buat dari scratch**: Jika ingin menggunakan selain TailwindCSS
3. **Gunakan command**: Tersedia command yang membuat struktur folder yang benar

```bash
php artisan app:theme:create
```

Command ini akan menyalin default theme dan mengupdate namespaces.

### Struktur Folder

Semua file yang dapat Anda edit berada di folder `themes`. Anda dapat melihat contoh struktur di [folder default theme](https://github.com/Paymenter/Paymenter/tree/master/themes/default).

#### Kustomisasi Extension Views

Untuk mengkustomisasi views extension, buat folder `vendor` di dalam folder views Anda.

Format normal extension: `<extension-type>.<extension-name>.<view-name>`

**Contoh**: Stripe pay modal menggunakan `gateways.stripe::pay`. Anda dapat mengedit view ini dengan membuat file:
```
vendor/gateways/stripe/pay.blade.php
```

### Building Assets

Paymenter dibangun menggunakan Tailwind CSS. Jika Anda menambahkan class baru ke theme, Anda perlu build ulang assets.

#### Install Dependencies

```bash
# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo bash -
sudo apt install nodejs

# Install dependencies
cd /var/www/paymenter
npm install
```

#### Build Assets

```bash
# Build assets untuk default theme
npm run build

# Build assets untuk theme tertentu
npm run build <theme-name>
```

---

## Kesimpulan

Dokumentasi ini memberikan panduan lengkap untuk mengembangkan extensions dan themes di Paymenter. Dengan memahami struktur hooks, konfigurasi, dan lifecycle yang tersedia, Anda dapat membuat extensions yang powerful dan themes yang menarik untuk sistem Paymenter.

Untuk informasi lebih lanjut, kunjungi [dokumentasi resmi Paymenter](https://paymenter.org/development/).