
<!DOCTYPE html>
<html lang="en" >
    
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="PUaNyWTHvRD0nvFCMEtkKA1dLpZBoX0dTpxcs3Zt">
    <title>
        Nex
                    - Starter
            </title>
    <link rel="preload" as="style" href="http://**************/materially/assets/app-DxuweRtB.css" /><link rel="modulepreload" href="http://**************/materially/assets/app-rZxx_yNx.js" /><link rel="stylesheet" href="http://**************/materially/assets/app-DxuweRtB.css" data-navigate-track="reload" /><script type="module" src="http://**************/materially/assets/app-rZxx_yNx.js" data-navigate-track="reload"></script>    <style>
    :root {
        /* Branding Colors (Light) */
        --color-primary: 229 100% 64%;
        --color-secondary: 237 33% 60%;

        /* Neutral Colors - Borders, Accents... (Light) */
        --color-neutral: 220 25% 85%;

        /* Text Colors (Light) */
        --color-base: 0 0% 0%;
        --color-muted: 220 28% 25%;
        --color-inverted: 100 100% 100%;

        /* State Colors */
        --color-success: 142 71% 45%;
        --color-error: 0 75% 60%;
        --color-warning: 25 95% 53%;
        --color-inactive: 0 0% 63%;
        --color-info: 210 100% 60%;

        /* Background Colors (Light) */
        --color-background: 100 100% 100%;
        --color-background-secondary: 0 0% 97%;
    }

    .dark {
        /* Branding Colors (Dark) */
        --color-primary: 229 100% 64%;
        --color-secondary: 237 33% 60%;

        /* Neutral Colors - Borders, Accents... (Dark) */
        --color-neutral: 220 25% 29%;

        /* Text Colors (Dark) */
        --color-base: 100 100% 100%;
        --color-muted: 220 28% 25%;
        --color-inverted: 220 14% 60%;

        /* Background Colors (Dark) */
        --color-background: 221 39% 11%;
        --color-background-secondary: 217 33% 16%;
    }
</style>
            <meta content="Nex - Starter" property="og:title">
    <meta content="Nex - Starter" name="title">
               
    <meta name="theme-color" content="">

    
<!-- Livewire Styles --><style >[wire\:loading][wire\:loading], [wire\:loading\.delay][wire\:loading\.delay], [wire\:loading\.inline-block][wire\:loading\.inline-block], [wire\:loading\.inline][wire\:loading\.inline], [wire\:loading\.block][wire\:loading\.block], [wire\:loading\.flex][wire\:loading\.flex], [wire\:loading\.table][wire\:loading\.table], [wire\:loading\.grid][wire\:loading\.grid], [wire\:loading\.inline-flex][wire\:loading\.inline-flex] {display: none;}[wire\:loading\.delay\.none][wire\:loading\.delay\.none], [wire\:loading\.delay\.shortest][wire\:loading\.delay\.shortest], [wire\:loading\.delay\.shorter][wire\:loading\.delay\.shorter], [wire\:loading\.delay\.short][wire\:loading\.delay\.short], [wire\:loading\.delay\.default][wire\:loading\.delay\.default], [wire\:loading\.delay\.long][wire\:loading\.delay\.long], [wire\:loading\.delay\.longer][wire\:loading\.delay\.longer], [wire\:loading\.delay\.longest][wire\:loading\.delay\.longest] {display: none;}[wire\:offline][wire\:offline] {display: none;}[wire\:dirty]:not(textarea):not(input):not(select) {display: none;}:root {--livewire-progress-bar-color: hsl(var(--color-primary));}[x-cloak] {display: none !important;}[wire\:cloak] {display: none !important;}</style>
</head>

<body class="w-full bg-background text-base min-h-screen flex flex-col antialiased" x-cloak x-data="{darkMode: $persist(window.matchMedia('(prefers-color-scheme: dark)').matches)}" :class="{'dark': darkMode}">
    
    <nav class="w-full px-4 lg:px-8 bg-background-secondary border-b border-neutral md:h-16 flex md:flex-row flex-col justify-between fixed top-0 z-20">
    <div x-data="{ 
        slideOverOpen: false 
    }"
        x-init="$watch('slideOverOpen', value => { document.documentElement.style.overflow = value ? 'hidden' : '' })"
        class="relative z-50 w-full h-auto">

        <div class="flex flex-row items-center justify-between w-full h-16">

            <div class="flex flex-row items-center">
                <a href="http://**************" class="flex flex-row items-center h-10" wire:navigate>
                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
                    <span class="text-xl font-bold leading-none flex items-center">Nex</span>
                </a>
                <div class="md:flex hidden flex-row ml-7">
                                                            <a href="http://**************" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80 flex items-center p-3"  wire:navigate >
    Home
</a>                                        
                                                            <div class="relative">
                        <div class="relative" x-data="{ open: false, adjustWidth: 0 }" x-init="$watch('open', value => {
    if (value) {
        adjustWidth = 0; // Reset adjustWidth when opening
        $nextTick(() => {
            let dropdown = $refs.dropdown;
            let rect = dropdown.getBoundingClientRect();
            let windowWidth = window.innerWidth;
            adjustWidth = rect.right > windowWidth ? rect.width - 40 : 0;
        });
    }
})">

    <button
        class="flex flex-row items-center px-2 py-1 text-sm font-semibold whitespace-nowrap text-base hover:text-base/80"
        x-on:click="open = !open">
        <div class="flex flex-col">
                                    <span class="flex flex-row items-center p-3 text-sm font-semibold whitespace-nowrap text-base hover:text-base/80">
                                        Shop
                                    </span>
                                </div>
        <svg x-bind:class="{ '-rotate-180' : open }" class="md:block hidden size-4 text-base ease-out duration-300" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"/></svg>    </button>

    <div x-ref="dropdown"
        class="absolute mt-2 w-48 px-2 py-1 bg-background-secondary rounded-md shadow-lg z-10 border border-neutral"
        x-bind:style="{
            left: `-${adjustWidth}px`,
        }"
        x-show="open"
        x-transition:enter="transition ease-out duration-150" x-transition:enter-start="opacity-0 scale-90"
        x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90"
        x-on:click.outside="open = false" x-cloak>
        <a href="http://**************/products/vps" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    VPS
</a>
    </div>
</div>                    </div>
                                        
                    
                </div>
            </div>

            <div class="flex flex-row items-center">
                <div class="items-center hidden md:flex mr-3">
                    <div class="relative" x-data="{ open: false, adjustWidth: 0 }" x-init="$watch('open', value => {
    if (value) {
        adjustWidth = 0; // Reset adjustWidth when opening
        $nextTick(() => {
            let dropdown = $refs.dropdown;
            let rect = dropdown.getBoundingClientRect();
            let windowWidth = window.innerWidth;
            adjustWidth = rect.right > windowWidth ? rect.width - 40 : 0;
        });
    }
})">

    <button
        class="flex flex-row items-center px-2 py-1 text-sm font-semibold whitespace-nowrap text-base hover:text-base/80"
        x-on:click="open = !open">
        <div class="flex flex-col">
                                <span class="text-sm text-base font-semibold text-nowrap">EN <span class="text-base/50 font-semibold">|</span> IDR</span>
                            </div>
        <svg x-bind:class="{ '-rotate-180' : open }" class="md:block hidden size-4 text-base ease-out duration-300" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"/></svg>    </button>

    <div x-ref="dropdown"
        class="absolute mt-2 w-48 px-2 py-1 bg-background-secondary rounded-md shadow-lg z-10 border border-neutral"
        x-bind:style="{
            left: `-${adjustWidth}px`,
        }"
        x-show="open"
        x-transition:enter="transition ease-out duration-150" x-transition:enter-start="opacity-0 scale-90"
        x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90"
        x-on:click.outside="open = false" x-cloak>
        <strong class="block p-2 text-xs font-semibold uppercase text-base/50"> Language </strong>
                            <div wire:snapshot="{&quot;data&quot;:{&quot;currentLocale&quot;:&quot;en&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;kmRKMvpe8ouSTsudachZ&quot;,&quot;name&quot;:&quot;components.language-switch&quot;,&quot;path&quot;:&quot;products\/vps\/starter\/checkout&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;323c4090f45a77da07462c23d9e64981a2e17051a392bfcd17f98ffcf5ae8d5d&quot;}" wire:effects="[]" wire:id="kmRKMvpe8ouSTsudachZ"
    x-data="{
        selectOpen: false,
        selectedValue: window.Livewire.find('kmRKMvpe8ouSTsudachZ').entangle('currentLocale').live,
        selectableItems: [{&quot;value&quot;:&quot;ar&quot;,&quot;label&quot;:&quot;Arabic&quot;},{&quot;value&quot;:&quot;de&quot;,&quot;label&quot;:&quot;German&quot;},{&quot;value&quot;:&quot;en&quot;,&quot;label&quot;:&quot;English&quot;},{&quot;value&quot;:&quot;es&quot;,&quot;label&quot;:&quot;Spanish&quot;},{&quot;value&quot;:&quot;fi&quot;,&quot;label&quot;:&quot;Finnish&quot;},{&quot;value&quot;:&quot;fr&quot;,&quot;label&quot;:&quot;French&quot;},{&quot;value&quot;:&quot;it&quot;,&quot;label&quot;:&quot;Italian&quot;},{&quot;value&quot;:&quot;sv&quot;,&quot;label&quot;:&quot;Swedish&quot;},{&quot;value&quot;:&quot;uk&quot;,&quot;label&quot;:&quot;Ukrainian&quot;},{&quot;value&quot;:&quot;ko&quot;,&quot;label&quot;:&quot;Korean&quot;},{&quot;value&quot;:&quot;lv&quot;,&quot;label&quot;:&quot;Latvian&quot;},{&quot;value&quot;:&quot;no&quot;,&quot;label&quot;:&quot;Norwegian&quot;},{&quot;value&quot;:&quot;pt&quot;,&quot;label&quot;:&quot;Portuguese&quot;},{&quot;value&quot;:&quot;sr&quot;,&quot;label&quot;:&quot;Serbian&quot;}],
        selectableItemActive: null,
        selectId: $id('select'),
        selectDropdownPosition: 'bottom',

        init() {
            this.$watch('selectOpen', () => {
                if (this.selectOpen) {
                    this.selectableItemActive = this.selectableItems.find(item => item.value === this.selectedValue) 
                        || this.selectableItems[0];
                    this.$nextTick(() => this.selectScrollToActiveItem());
                }
                this.selectPositionUpdate();
            });

            window.addEventListener('resize', () => this.selectPositionUpdate());
        },

        selectableItemIsActive(item) {
            return this.selectableItemActive && this.selectableItemActive.value === item.value;
        },

        selectableItemActiveNext() {
            let index = this.selectableItems.indexOf(this.selectableItemActive);
            if (index < this.selectableItems.length - 1) {
                this.selectableItemActive = this.selectableItems[index + 1];
                this.selectScrollToActiveItem();
            }
        },

        selectableItemActivePrevious() {
            let index = this.selectableItems.indexOf(this.selectableItemActive);
            if (index > 0) {
                this.selectableItemActive = this.selectableItems[index - 1];
                this.selectScrollToActiveItem();
            }
        },

        selectScrollToActiveItem() {
            if (this.selectableItemActive) {
                const activeElement = document.getElementById(this.selectableItemActive.value + '-' + this.selectId);
                if (activeElement) {
                    activeElement.scrollIntoView({ block: 'nearest' });
                }
            }
        },

        selectPositionUpdate() {
            const selectDropdownBottomPos = this.$refs.selectButton.getBoundingClientRect().top + 
                this.$refs.selectButton.offsetHeight + 
                this.$refs.selectableItemsList.offsetHeight;
            
            this.selectDropdownPosition = window.innerHeight < selectDropdownBottomPos ? 'top' : 'bottom';
        }
    }"
    @keydown.escape="selectOpen = false"
    @keydown.down.prevent="if(selectOpen) { selectableItemActiveNext() } else { selectOpen = true }"
    @keydown.up.prevent="if(selectOpen) { selectableItemActivePrevious() } else { selectOpen = true }"
    @keydown.enter.prevent="selectedValue = selectableItemActive.value; selectOpen = false"
    class="relative w-full"
>
    <button 
        x-ref="selectButton"
        @click="selectOpen = !selectOpen"
        :class="{ 'ring-2 ring-offset-2 ring-neutral-800': selectOpen }"
        class="relative w-full min-h-[38px] py-2 pl-3 pr-10 text-left bg-background-secondary border border-neutral rounded-md shadow-sm cursor-pointer text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-800"
        type="button"
    >
        <span x-text="selectableItems.find(item => item.value === selectedValue)?.label ?? 'Select language'" class="block truncate"></span>
        <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg class="size-4" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M18.2072 9.0428 12.0001 2.83569 5.793 9.0428 7.20721 10.457 12.0001 5.66412 16.793 10.457 18.2072 9.0428ZM5.79285 14.9572 12 21.1643 18.2071 14.9572 16.7928 13.543 12 18.3359 7.20706 13.543 5.79285 14.9572Z"/></svg>        </span>
    </button>

    <ul
        x-show="selectOpen"
        x-ref="selectableItemsList"
        @click.away="selectOpen = false"
        x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        :class="{ 'bottom-full mb-1': selectDropdownPosition === 'top', 'top-full mt-1': selectDropdownPosition === 'bottom' }"
        class="absolute z-50 w-full py-1 overflow-auto bg-background-secondary border border-neutral rounded-lg shadow-lg max-h-60 focus:outline-none text-sm"
        x-cloak
    >
        <template x-for="item in selectableItems" :key="item.value">
            <li
                :id="item.value + '-' + selectId"
                @click="selectedValue = item.value; selectOpen = false"
                @mousemove="selectableItemActive = item"
                :class="{ 'bg-neutral-700': selectableItemIsActive(item) }"
                class="relative py-2 pl-8 pr-4 cursor-pointer select-none hover:bg-neutral-700"
            >
                <span 
                    class="block truncate"
                    :class="{ 'font-semibold': item.value === selectedValue }"
                    x-text="item.label"
                ></span>
                <span
                    x-show="item.value === selectedValue"
                    class="absolute inset-y-0 left-0 flex items-center pl-2 text-neutral-400"
                >
                    <svg class="size-4" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M9.9997 15.1709L19.1921 5.97852L20.6063 7.39273L9.9997 17.9993L3.63574 11.6354L5.04996 10.2212L9.9997 15.1709Z"/></svg>                </span>
            </li>
        </template>
    </ul>
</div>                            <div wire:snapshot="{&quot;data&quot;:{&quot;currentCurrency&quot;:&quot;IDR&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;BmItRYn2bOcXGSca17eO&quot;,&quot;name&quot;:&quot;components.currency-switch&quot;,&quot;path&quot;:&quot;products\/vps\/starter\/checkout&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;e77262b22a21db24cc23e504ffc75732ef91f1f7c9feee7dc92b8361fd589e46&quot;}" wire:effects="[]" wire:id="BmItRYn2bOcXGSca17eO"></div>
    </div>
</div>                    <button @click="darkMode = !darkMode" type="button" class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-neutral transition">
    <template x-if="!darkMode">
        <svg class="size-4 text-base" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M12 18C8.68629 18 6 15.3137 6 12C6 8.68629 8.68629 6 12 6C15.3137 6 18 8.68629 18 12C18 15.3137 15.3137 18 12 18ZM11 1H13V4H11V1ZM11 20H13V23H11V20ZM3.51472 4.92893L4.92893 3.51472L7.05025 5.63604L5.63604 7.05025L3.51472 4.92893ZM16.9497 18.364L18.364 16.9497L20.4853 19.0711L19.0711 20.4853L16.9497 18.364ZM19.0711 3.51472L20.4853 4.92893L18.364 7.05025L16.9497 5.63604L19.0711 3.51472ZM5.63604 16.9497L7.05025 18.364L4.92893 20.4853L3.51472 19.0711L5.63604 16.9497ZM23 11V13H20V11H23ZM4 11V13H1V11H4Z"/></svg>    </template>
    <template x-if="darkMode">
        <svg class="size-4 text-base" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.3807 2.01886C9.91573 3.38768 9 5.3369 9 7.49999C9 11.6421 12.3579 15 16.5 15C18.6631 15 20.6123 14.0843 21.9811 12.6193C21.6613 17.8537 17.3149 22 12 22C6.47715 22 2 17.5228 2 12C2 6.68514 6.14629 2.33869 11.3807 2.01886Z"/></svg>    </template>
</button>
                </div>

                <!--[if BLOCK]><![endif]--><div class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-neutral transition">
    <a wire:snapshot="{&quot;data&quot;:{&quot;cartCount&quot;:1},&quot;memo&quot;:{&quot;id&quot;:&quot;Bm8GvdxObAZGZYOEBJR4&quot;,&quot;name&quot;:&quot;components.cart&quot;,&quot;path&quot;:&quot;products\/vps\/starter\/checkout&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;5367703fc42e4d7c9f7405956f0fd330774fdb3af59fe5aae953b2ce53f4b829&quot;}" wire:effects="{&quot;listeners&quot;:[&quot;cartUpdated&quot;]}" wire:id="Bm8GvdxObAZGZYOEBJR4" href="http://**************/cart" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    <svg class="size-4" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M9 6C9 4.34315 10.3431 3 12 3C13.6569 3 15 4.34315 15 6H9ZM7 6H4C3.44772 6 3 6.44772 3 7V21C3 21.5523 3.44772 22 4 22H20C20.5523 22 21 21.5523 21 21V7C21 6.44772 20.5523 6 20 6H17C17 3.23858 14.7614 1 12 1C9.23858 1 7 3.23858 7 6ZM9 10C9 11.6569 10.3431 13 12 13C13.6569 13 15 11.6569 15 10H17C17 12.7614 14.7614 15 12 15C9.23858 15 7 12.7614 7 10H9Z"/></svg>
</a></div>
<!--[if ENDBLOCK]><![endif]-->
                                <div class="hidden lg:flex">
                    <div class="relative" x-data="{ open: false, adjustWidth: 0 }" x-init="$watch('open', value => {
    if (value) {
        adjustWidth = 0; // Reset adjustWidth when opening
        $nextTick(() => {
            let dropdown = $refs.dropdown;
            let rect = dropdown.getBoundingClientRect();
            let windowWidth = window.innerWidth;
            adjustWidth = rect.right > windowWidth ? rect.width - 40 : 0;
        });
    }
})">

    <button
        class="flex flex-row items-center px-2 py-1 text-sm font-semibold whitespace-nowrap text-base hover:text-base/80"
        x-on:click="open = !open">
        <img src="https://www.gravatar.com/avatar/9c114195027b43d81d6d4cb723fffcff?d=wavatar" class="size-8 rounded-full border border-neutral bg-background" alt="avatar" />
        <svg x-bind:class="{ '-rotate-180' : open }" class="md:block hidden size-4 text-base ease-out duration-300" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"/></svg>    </button>

    <div x-ref="dropdown"
        class="absolute mt-2 w-48 px-2 py-1 bg-background-secondary rounded-md shadow-lg z-10 border border-neutral"
        x-bind:style="{
            left: `-${adjustWidth}px`,
        }"
        x-show="open"
        x-transition:enter="transition ease-out duration-150" x-transition:enter-start="opacity-0 scale-90"
        x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90"
        x-on:click.outside="open = false" x-cloak>
        <div class="flex flex-col p-2">
                                <span class="text-sm text-base break-words">doko sundoko</span>
                                <span class="text-sm text-base break-words"><EMAIL></span>
                            </div>
                                                        <a href="http://**************/dashboard" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    Dashboard
</a>                                                        <a href="http://**************/tickets" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    Tickets
</a>                                                        <a href="http://**************/account" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    Account
</a>                                                        <a href="http://**************/admin" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80" >
    Admin
</a>                                                        <div wire:snapshot="{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;I3gXKpMNuaJMbSWiBAYa&quot;,&quot;name&quot;:&quot;auth.logout&quot;,&quot;path&quot;:&quot;products\/vps\/starter\/checkout&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c7550b65b7b6d7be7e9eaf9ff772041079f18e23aba3237edd1cfad6ee54997f&quot;}" wire:effects="[]" wire:id="I3gXKpMNuaJMbSWiBAYa">
    <button wire:click="logout" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-error/80 hover:text-error bg-background-secondary hover:bg-background-secondary/80">
        Logout
    </button>
</div>
    </div>
</div>                </div>
                                <button
                    @click="slideOverOpen = !slideOverOpen"
                    class="relative w-10 h-10 flex lg:hidden items-center justify-center rounded-lg hover:bg-neutral transition"
                    aria-label="Toggle Menu">

                    <span
                        x-show="!slideOverOpen"
                        x-transition:enter="transition duration-300"
                        x-transition:enter-start="opacity-0 -rotate-90 scale-75"
                        x-transition:enter-end="opacity-100 rotate-0 scale-100"
                        x-transition:leave="transition duration-150"
                        x-transition:leave-start="opacity-100 rotate-0 scale-100"
                        x-transition:leave-end="opacity-0 rotate-90 scale-75"
                        class="absolute inset-0 flex items-center justify-center"
                        aria-hidden="true">
                        <svg class="size-5" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M3 4H21V6H3V4ZM3 11H21V13H3V11ZM3 18H21V20H3V18Z"/></svg>                    </span>

                    <span
                        x-show="slideOverOpen"
                        x-transition:enter="transition duration-300"
                        x-transition:enter-start="opacity-0 rotate-90 scale-75"
                        x-transition:enter-end="opacity-100 rotate-0 scale-100"
                        x-transition:leave="transition duration-150"
                        x-transition:leave-start="opacity-100 rotate-0 scale-100"
                        x-transition:leave-end="opacity-0 -rotate-90 scale-75"
                        class="absolute inset-0 flex items-center justify-center"
                        aria-hidden="true">
                        <svg class="size-5" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.9997 10.5865L16.9495 5.63672L18.3637 7.05093L13.4139 12.0007L18.3637 16.9504L16.9495 18.3646L11.9997 13.4149L7.04996 18.3646L5.63574 16.9504L10.5855 12.0007L5.63574 7.05093L7.04996 5.63672L11.9997 10.5865Z"/></svg>                    </span>

                </button>
            </div>
        </div>
        <template x-teleport="body">
            <div
                x-show="slideOverOpen"
                @keydown.window.escape="slideOverOpen=false"
                x-cloak
                class="fixed left-0 right-0 top-16 w-full z-[99]"
                style="height:calc(100dvh - 4rem);"
                aria-modal="true"
                tabindex="-1">
                <div
                    x-show="slideOverOpen"
                    @click.away="slideOverOpen = false"
                    x-transition.opacity.duration.300ms
                    class="absolute inset-0 bg-background-secondary border-t border-neutral shadow-lg overflow-y-auto flex flex-col">

                    <div class="flex flex-col h-full p-4">
                        <div class="flex-1 min-h-0 overflow-y-auto">
                            <div class="lg:px-4 lg:py-6 flex flex-col gap-2">
    <div class="flex flex-col gap-2 md:hidden">
                        <div class="flex items-center rounded-lg hover:bg-primary/5">
            <a href="http://**************" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80 w-full"  wire:navigate >
    <svg class="size-5 fill-base/50" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M19 21H5C4.44772 21 4 20.5523 4 20V11L1 11L11.3273 1.6115C11.7087 1.26475 12.2913 1.26475 12.6727 1.6115L23 11L20 11V20C20 20.5523 19.5523 21 19 21ZM6 19H18V9.15745L12 3.7029L6 9.15745V19Z"/></svg>                                Home
</a>        </div>
                                        <div x-data="{ activeAccordion: false }"
            class="relative w-full mx-auto overflow-hidden text-sm font-normal divide-y divide-gray-200">
            <div class="cursor-pointer">
                <button @click="activeAccordion = !activeAccordion"
                    class="flex items-center justify-between w-full p-3 text-sm font-semibold whitespace-nowrap rounded-lg hover:bg-primary/5">
                    <div class="flex flex-row gap-2">
                                                    <svg class="size-5 fill-base/50" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M7.00488 7.99966V5.99966C7.00488 3.23824 9.24346 0.999664 12.0049 0.999664C14.7663 0.999664 17.0049 3.23824 17.0049 5.99966V7.99966H20.0049C20.5572 7.99966 21.0049 8.44738 21.0049 8.99966V20.9997C21.0049 21.5519 20.5572 21.9997 20.0049 21.9997H4.00488C3.4526 21.9997 3.00488 21.5519 3.00488 20.9997V8.99966C3.00488 8.44738 3.4526 7.99966 4.00488 7.99966H7.00488ZM7.00488 9.99966H5.00488V19.9997H19.0049V9.99966H17.0049V11.9997H15.0049V9.99966H9.00488V11.9997H7.00488V9.99966ZM9.00488 7.99966H15.0049V5.99966C15.0049 4.34281 13.6617 2.99966 12.0049 2.99966C10.348 2.99966 9.00488 4.34281 9.00488 5.99966V7.99966Z"/></svg>                                                <span>Shop</span>
                    </div>
                    <svg x-bind:class="{ 'rotate-180': activeAccordion }" class="size-4 text-base ease-out duration-300" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"/></svg>                </button>
                <div x-show="activeAccordion" x-collapse x-cloak>
                    <div class="p-4 pt-0 opacity-70">
                                                <div class="flex items-center space-x-2">
                            <a href="http://**************/products/vps" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    VPS
</a>                        </div>
                                            </div>
                </div>
            </div>
        </div>
                        <div class="h-px w-full bg-neutral"></div>
                    </div>

    <div class="flex flex-col gap-2">
                        <div class="flex items-center rounded-lg hover:bg-primary/5">
            <a href="http://**************/dashboard" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80 w-full"  wire:navigate >
    <svg class="size-5 fill-base/50" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M3 4C3 3.44772 3.44772 3 4 3H10C10.5523 3 11 3.44772 11 4V10C11 10.5523 10.5523 11 10 11H4C3.44772 11 3 10.5523 3 10V4ZM3 14C3 13.4477 3.44772 13 4 13H10C10.5523 13 11 13.4477 11 14V20C11 20.5523 10.5523 21 10 21H4C3.44772 21 3 20.5523 3 20V14ZM13 4C13 3.44772 13.4477 3 14 3H20C20.5523 3 21 3.44772 21 4V10C21 10.5523 20.5523 11 20 11H14C13.4477 11 13 10.5523 13 10V4ZM13 14C13 13.4477 13.4477 13 14 13H20C20.5523 13 21 13.4477 21 14V20C21 20.5523 20.5523 21 20 21H14C13.4477 21 13 20.5523 13 20V14ZM15 5V9H19V5H15ZM15 15V19H19V15H15ZM5 5V9H9V5H5ZM5 15V19H9V15H5Z"/></svg>                                Dashboard
</a>        </div>
                                        <div class="flex items-center rounded-lg hover:bg-primary/5">
            <a href="http://**************/services" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80 w-full"  wire:navigate >
    <svg class="size-5 fill-base/50" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M4 5H20V3H4V5ZM20 9H4V7H20V9ZM3 11H10V13H14V11H21V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V11ZM16 13V15H8V13H5V19H19V13H16Z"/></svg>                                Services
</a>        </div>
                                        <div class="flex items-center rounded-lg hover:bg-primary/5">
            <a href="http://**************/invoices" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80 w-full"  wire:navigate >
    <svg class="size-5 fill-base/50" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M9 4L6 2L3 4V19C3 20.6569 4.34315 22 6 22H20C21.6569 22 23 20.6569 23 19V16H21V4L18 2L15 4L12 2L9 4ZM19 16H7V19C7 19.5523 6.55228 20 6 20C5.44772 20 5 19.5523 5 19V5.07037L6 4.4037L9 6.4037L12 4.4037L15 6.4037L18 4.4037L19 5.07037V16ZM20 20H8.82929C8.93985 19.6872 9 19.3506 9 19V18H21V19C21 19.5523 20.5523 20 20 20Z"/></svg>                                Invoices
</a>        </div>
                        <div class="h-px w-full bg-neutral"></div>
                                <div class="flex items-center rounded-lg hover:bg-primary/5">
            <a href="http://**************/tickets" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80 w-full"  wire:navigate >
    <svg class="size-5 fill-base/50" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M22 17.0022C21.999 19.8731 19.9816 22.2726 17.2872 22.8616L16.6492 20.9476C17.8532 20.7511 18.8765 20.0171 19.4649 19H17C15.8954 19 15 18.1046 15 17V13C15 11.8954 15.8954 11 17 11H19.9381C19.446 7.05369 16.0796 4 12 4C7.92038 4 4.55399 7.05369 4.06189 11H7C8.10457 11 9 11.8954 9 13V17C9 18.1046 8.10457 19 7 19H4C2.89543 19 2 18.1046 2 17V12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12V12.9987V13V17V17.0013V17.0022ZM20 17V13H17V17H20ZM4 13V17H7V13H4Z"/></svg>                                Tickets
</a>        </div>
                        <div class="h-px w-full bg-neutral"></div>
                                <div x-data="{ activeAccordion: false }"
            class="relative w-full mx-auto overflow-hidden text-sm font-normal divide-y divide-gray-200">
            <div class="cursor-pointer">
                <button @click="activeAccordion = !activeAccordion"
                    class="flex items-center justify-between w-full p-3 text-sm font-semibold whitespace-nowrap rounded-lg hover:bg-primary/5">
                    <div class="flex flex-row gap-2">
                                                    <svg class="size-5 fill-base/50" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M3.33946 17.0002C2.90721 16.2515 2.58277 15.4702 2.36133 14.6741C3.3338 14.1779 3.99972 13.1668 3.99972 12.0002C3.99972 10.8345 3.3348 9.824 2.36353 9.32741C2.81025 7.71651 3.65857 6.21627 4.86474 4.99001C5.7807 5.58416 6.98935 5.65534 7.99972 5.072C9.01009 4.48866 9.55277 3.40635 9.4962 2.31604C11.1613 1.8846 12.8847 1.90004 14.5031 2.31862C14.4475 3.40806 14.9901 4.48912 15.9997 5.072C17.0101 5.65532 18.2187 5.58416 19.1346 4.99007C19.7133 5.57986 20.2277 6.25151 20.66 7.00021C21.0922 7.7489 21.4167 8.53025 21.6381 9.32628C20.6656 9.82247 19.9997 10.8336 19.9997 12.0002C19.9997 13.166 20.6646 14.1764 21.6359 14.673C21.1892 16.2839 20.3409 17.7841 19.1347 19.0104C18.2187 18.4163 17.0101 18.3451 15.9997 18.9284C14.9893 19.5117 14.4467 20.5941 14.5032 21.6844C12.8382 22.1158 11.1148 22.1004 9.49633 21.6818C9.55191 20.5923 9.00929 19.5113 7.99972 18.9284C6.98938 18.3451 5.78079 18.4162 4.86484 19.0103C4.28617 18.4205 3.77172 17.7489 3.33946 17.0002ZM8.99972 17.1964C10.0911 17.8265 10.8749 18.8227 11.2503 19.9659C11.7486 20.0133 12.2502 20.014 12.7486 19.9675C13.1238 18.8237 13.9078 17.8268 14.9997 17.1964C16.0916 16.5659 17.347 16.3855 18.5252 16.6324C18.8146 16.224 19.0648 15.7892 19.2729 15.334C18.4706 14.4373 17.9997 13.2604 17.9997 12.0002C17.9997 10.74 18.4706 9.5632 19.2729 8.6665C19.1688 8.4405 19.0538 8.21822 18.9279 8.00021C18.802 7.78219 18.667 7.57148 18.5233 7.36842C17.3457 7.61476 16.0911 7.43414 14.9997 6.80405C13.9083 6.17395 13.1246 5.17768 12.7491 4.03455C12.2509 3.98714 11.7492 3.98646 11.2509 4.03292C10.8756 5.17671 10.0916 6.17364 8.99972 6.80405C7.9078 7.43447 6.65245 7.61494 5.47428 7.36803C5.18485 7.77641 4.93463 8.21117 4.72656 8.66637C5.52881 9.56311 5.99972 10.74 5.99972 12.0002C5.99972 13.2604 5.52883 14.4372 4.72656 15.3339C4.83067 15.5599 4.94564 15.7822 5.07152 16.0002C5.19739 16.2182 5.3324 16.4289 5.47612 16.632C6.65377 16.3857 7.90838 16.5663 8.99972 17.1964ZM11.9997 15.0002C10.3429 15.0002 8.99972 13.6571 8.99972 12.0002C8.99972 10.3434 10.3429 9.00021 11.9997 9.00021C13.6566 9.00021 14.9997 10.3434 14.9997 12.0002C14.9997 13.6571 13.6566 15.0002 11.9997 15.0002ZM11.9997 13.0002C12.552 13.0002 12.9997 12.5525 12.9997 12.0002C12.9997 11.4479 12.552 11.0002 11.9997 11.0002C11.4474 11.0002 10.9997 11.4479 10.9997 12.0002C10.9997 12.5525 11.4474 13.0002 11.9997 13.0002Z"/></svg>                                                <span>Account</span>
                    </div>
                    <svg x-bind:class="{ 'rotate-180': activeAccordion }" class="size-4 text-base ease-out duration-300" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"/></svg>                </button>
                <div x-show="activeAccordion" x-collapse x-cloak>
                    <div class="p-4 pt-0 opacity-70">
                                                                                <div class="flex items-center space-x-2">
                                <a href="http://**************/account" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    Personal Details
</a>                            </div>
                                                                                                            <div class="flex items-center space-x-2">
                                <a href="http://**************/account/security" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    Security
</a>                            </div>
                                                                                                                            </div>
                </div>
            </div>
        </div>
                                <div class="flex flex-row items-center mt-4 justify-between md:hidden">
            <div class="relative" x-data="{ open: false, adjustWidth: 0 }" x-init="$watch('open', value => {
    if (value) {
        adjustWidth = 0; // Reset adjustWidth when opening
        $nextTick(() => {
            let dropdown = $refs.dropdown;
            let rect = dropdown.getBoundingClientRect();
            let windowWidth = window.innerWidth;
            adjustWidth = rect.right > windowWidth ? rect.width - 40 : 0;
        });
    }
})">

    <button
        class="flex flex-row items-center px-2 py-1 text-sm font-semibold whitespace-nowrap text-base hover:text-base/80"
        x-on:click="open = !open">
        <div class="flex flex-col">
                        <span class="text-sm text-base font-semibold text-nowrap">EN <span class="text-base/50 font-semibold">|</span> IDR</span>
                    </div>
        <svg x-bind:class="{ '-rotate-180' : open }" class="md:block hidden size-4 text-base ease-out duration-300" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"/></svg>    </button>

    <div x-ref="dropdown"
        class="absolute mt-2 w-48 px-2 py-1 bg-background-secondary rounded-md shadow-lg z-10 border border-neutral"
        x-bind:style="{
            left: `-${adjustWidth}px`,
        }"
        x-show="open"
        x-transition:enter="transition ease-out duration-150" x-transition:enter-start="opacity-0 scale-90"
        x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90"
        x-on:click.outside="open = false" x-cloak>
        <strong class="block p-2 text-xs font-semibold uppercase text-base/50"> Language </strong>
                    <div wire:snapshot="{&quot;data&quot;:{&quot;currentLocale&quot;:&quot;en&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;5hZL3BFoDpBYoedmO7I6&quot;,&quot;name&quot;:&quot;components.language-switch&quot;,&quot;path&quot;:&quot;products\/vps\/starter\/checkout&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;d9bebe000e6ed03b1e8ce83b3f9795753a65c33a06e05991c39d4998c6d9219a&quot;}" wire:effects="[]" wire:id="5hZL3BFoDpBYoedmO7I6"
    x-data="{
        selectOpen: false,
        selectedValue: window.Livewire.find('5hZL3BFoDpBYoedmO7I6').entangle('currentLocale').live,
        selectableItems: [{&quot;value&quot;:&quot;ar&quot;,&quot;label&quot;:&quot;Arabic&quot;},{&quot;value&quot;:&quot;de&quot;,&quot;label&quot;:&quot;German&quot;},{&quot;value&quot;:&quot;en&quot;,&quot;label&quot;:&quot;English&quot;},{&quot;value&quot;:&quot;es&quot;,&quot;label&quot;:&quot;Spanish&quot;},{&quot;value&quot;:&quot;fi&quot;,&quot;label&quot;:&quot;Finnish&quot;},{&quot;value&quot;:&quot;fr&quot;,&quot;label&quot;:&quot;French&quot;},{&quot;value&quot;:&quot;it&quot;,&quot;label&quot;:&quot;Italian&quot;},{&quot;value&quot;:&quot;sv&quot;,&quot;label&quot;:&quot;Swedish&quot;},{&quot;value&quot;:&quot;uk&quot;,&quot;label&quot;:&quot;Ukrainian&quot;},{&quot;value&quot;:&quot;ko&quot;,&quot;label&quot;:&quot;Korean&quot;},{&quot;value&quot;:&quot;lv&quot;,&quot;label&quot;:&quot;Latvian&quot;},{&quot;value&quot;:&quot;no&quot;,&quot;label&quot;:&quot;Norwegian&quot;},{&quot;value&quot;:&quot;pt&quot;,&quot;label&quot;:&quot;Portuguese&quot;},{&quot;value&quot;:&quot;sr&quot;,&quot;label&quot;:&quot;Serbian&quot;}],
        selectableItemActive: null,
        selectId: $id('select'),
        selectDropdownPosition: 'bottom',

        init() {
            this.$watch('selectOpen', () => {
                if (this.selectOpen) {
                    this.selectableItemActive = this.selectableItems.find(item => item.value === this.selectedValue) 
                        || this.selectableItems[0];
                    this.$nextTick(() => this.selectScrollToActiveItem());
                }
                this.selectPositionUpdate();
            });

            window.addEventListener('resize', () => this.selectPositionUpdate());
        },

        selectableItemIsActive(item) {
            return this.selectableItemActive && this.selectableItemActive.value === item.value;
        },

        selectableItemActiveNext() {
            let index = this.selectableItems.indexOf(this.selectableItemActive);
            if (index < this.selectableItems.length - 1) {
                this.selectableItemActive = this.selectableItems[index + 1];
                this.selectScrollToActiveItem();
            }
        },

        selectableItemActivePrevious() {
            let index = this.selectableItems.indexOf(this.selectableItemActive);
            if (index > 0) {
                this.selectableItemActive = this.selectableItems[index - 1];
                this.selectScrollToActiveItem();
            }
        },

        selectScrollToActiveItem() {
            if (this.selectableItemActive) {
                const activeElement = document.getElementById(this.selectableItemActive.value + '-' + this.selectId);
                if (activeElement) {
                    activeElement.scrollIntoView({ block: 'nearest' });
                }
            }
        },

        selectPositionUpdate() {
            const selectDropdownBottomPos = this.$refs.selectButton.getBoundingClientRect().top + 
                this.$refs.selectButton.offsetHeight + 
                this.$refs.selectableItemsList.offsetHeight;
            
            this.selectDropdownPosition = window.innerHeight < selectDropdownBottomPos ? 'top' : 'bottom';
        }
    }"
    @keydown.escape="selectOpen = false"
    @keydown.down.prevent="if(selectOpen) { selectableItemActiveNext() } else { selectOpen = true }"
    @keydown.up.prevent="if(selectOpen) { selectableItemActivePrevious() } else { selectOpen = true }"
    @keydown.enter.prevent="selectedValue = selectableItemActive.value; selectOpen = false"
    class="relative w-full"
>
    <button 
        x-ref="selectButton"
        @click="selectOpen = !selectOpen"
        :class="{ 'ring-2 ring-offset-2 ring-neutral-800': selectOpen }"
        class="relative w-full min-h-[38px] py-2 pl-3 pr-10 text-left bg-background-secondary border border-neutral rounded-md shadow-sm cursor-pointer text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-800"
        type="button"
    >
        <span x-text="selectableItems.find(item => item.value === selectedValue)?.label ?? 'Select language'" class="block truncate"></span>
        <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg class="size-4" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M18.2072 9.0428 12.0001 2.83569 5.793 9.0428 7.20721 10.457 12.0001 5.66412 16.793 10.457 18.2072 9.0428ZM5.79285 14.9572 12 21.1643 18.2071 14.9572 16.7928 13.543 12 18.3359 7.20706 13.543 5.79285 14.9572Z"/></svg>        </span>
    </button>

    <ul
        x-show="selectOpen"
        x-ref="selectableItemsList"
        @click.away="selectOpen = false"
        x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        :class="{ 'bottom-full mb-1': selectDropdownPosition === 'top', 'top-full mt-1': selectDropdownPosition === 'bottom' }"
        class="absolute z-50 w-full py-1 overflow-auto bg-background-secondary border border-neutral rounded-lg shadow-lg max-h-60 focus:outline-none text-sm"
        x-cloak
    >
        <template x-for="item in selectableItems" :key="item.value">
            <li
                :id="item.value + '-' + selectId"
                @click="selectedValue = item.value; selectOpen = false"
                @mousemove="selectableItemActive = item"
                :class="{ 'bg-neutral-700': selectableItemIsActive(item) }"
                class="relative py-2 pl-8 pr-4 cursor-pointer select-none hover:bg-neutral-700"
            >
                <span 
                    class="block truncate"
                    :class="{ 'font-semibold': item.value === selectedValue }"
                    x-text="item.label"
                ></span>
                <span
                    x-show="item.value === selectedValue"
                    class="absolute inset-y-0 left-0 flex items-center pl-2 text-neutral-400"
                >
                    <svg class="size-4" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M9.9997 15.1709L19.1921 5.97852L20.6063 7.39273L9.9997 17.9993L3.63574 11.6354L5.04996 10.2212L9.9997 15.1709Z"/></svg>                </span>
            </li>
        </template>
    </ul>
</div>                    <div wire:snapshot="{&quot;data&quot;:{&quot;currentCurrency&quot;:&quot;IDR&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;wZ3dGqQnhQKbK6BLNK07&quot;,&quot;name&quot;:&quot;components.currency-switch&quot;,&quot;path&quot;:&quot;products\/vps\/starter\/checkout&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c23418521cc160b8f45b63e360f701d8e3bda7bea784b8d2c1f287b4f094c076&quot;}" wire:effects="[]" wire:id="wZ3dGqQnhQKbK6BLNK07"></div>
    </div>
</div>
            <button @click="darkMode = !darkMode" type="button" class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-neutral transition">
    <template x-if="!darkMode">
        <svg class="size-4 text-base" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M12 18C8.68629 18 6 15.3137 6 12C6 8.68629 8.68629 6 12 6C15.3137 6 18 8.68629 18 12C18 15.3137 15.3137 18 12 18ZM11 1H13V4H11V1ZM11 20H13V23H11V20ZM3.51472 4.92893L4.92893 3.51472L7.05025 5.63604L5.63604 7.05025L3.51472 4.92893ZM16.9497 18.364L18.364 16.9497L20.4853 19.0711L19.0711 20.4853L16.9497 18.364ZM19.0711 3.51472L20.4853 4.92893L18.364 7.05025L16.9497 5.63604L19.0711 3.51472ZM5.63604 16.9497L7.05025 18.364L4.92893 20.4853L3.51472 19.0711L5.63604 16.9497ZM23 11V13H20V11H23ZM4 11V13H1V11H4Z"/></svg>    </template>
    <template x-if="darkMode">
        <svg class="size-4 text-base" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.3807 2.01886C9.91573 3.38768 9 5.3369 9 7.49999C9 11.6421 12.3579 15 16.5 15C18.6631 15 20.6123 14.0843 21.9811 12.6193C21.6613 17.8537 17.3149 22 12 22C6.47715 22 2 17.5228 2 12C2 6.68514 6.14629 2.33869 11.3807 2.01886Z"/></svg>    </template>
</button>

        </div>
    </div>
</div>
                        </div>
                        <div class="mt-5">
                            
                            <div
                                x-data="{ userPanelOpen: false }"
                                @keydown.escape.window="userPanelOpen = false"
                                x-cloak
                                class="relative">

                                <button @click="userPanelOpen = true" aria-label="Open user menu" class="flex gap-4 items-center justify-start">
                                    <img src="https://www.gravatar.com/avatar/9c114195027b43d81d6d4cb723fffcff?d=wavatar" class="size-10 rounded-full border border-neutral bg-background" alt="avatar" />
                                    <div class="flex flex-col items-start gap-0.5">
                                        <span class="font-bold text-md">doko sundoko</span>
                                        <span class="text-sm text-base/70"><EMAIL></span>
                                    </div>
                                </button>

                                <div
                                    x-show="userPanelOpen"
                                    x-transition:enter="transition-opacity ease-out duration-300"
                                    x-transition:enter-start="opacity-0"
                                    x-transition:enter-end="opacity-60"
                                    x-transition:leave="transition-opacity ease-in duration-200"
                                    x-transition:leave-start="opacity-60"
                                    x-transition:leave-end="opacity-0"
                                    @click="userPanelOpen=false"
                                    class="fixed inset-0 bg-primary/5 backdrop-blur-xs z-40"
                                    style="pointer-events: auto"></div>

                                <div
                                    x-show="userPanelOpen"
                                    x-transition:enter="transition transform ease-out duration-300"
                                    x-transition:enter-start="translate-y-full opacity-0"
                                    x-transition:enter-end="translate-y-0 opacity-100"
                                    x-transition:leave="transition transform ease-in duration-200"
                                    x-transition:leave-start="translate-y-0 opacity-100"
                                    x-transition:leave-end="translate-y-full opacity-0"
                                    class="fixed bottom-0 left-0 right-0 z-50 mx-auto w-full"
                                    style="pointer-events: auto"
                                    @click.away="userPanelOpen = false"
                                    tabindex="-1"
                                    aria-modal="true">
                                    <div class="bg-background-secondary shadow-lg rounded-t-2xl border border-neutral p-6">
                                        <div class="flex gap-4 items-center justify-start">
                                            <img src="https://www.gravatar.com/avatar/9c114195027b43d81d6d4cb723fffcff?d=wavatar" class="size-12 rounded-full border border-neutral bg-background" alt="avatar" />
                                            <div class="flex flex-col gap-0.5">
                                                <span class="font-bold text-lg">doko sundoko</span>
                                                <span class="text-sm text-base/70"><EMAIL></span>
                                            </div>
                                        </div>
                                        <div class="h-px w-full bg-neutral my-6"></div>
                                        <div class="mt-4 flex flex-col gap-2 w-full">
                                                                                        <a href="http://**************/dashboard" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    Dashboard
</a>                                                                                        <a href="http://**************/tickets" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    Tickets
</a>                                                                                        <a href="http://**************/account" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80"  wire:navigate >
    Account
</a>                                                                                        <a href="http://**************/admin" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-wrap text-base hover:text-base/80" >
    Admin
</a>                                                                                        <div wire:snapshot="{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;r55yIa4MK2h3bZFCzn9h&quot;,&quot;name&quot;:&quot;auth.logout&quot;,&quot;path&quot;:&quot;products\/vps\/starter\/checkout&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;337f134c4597140d8e2e55a3992e1a1521fc9a5af797522a092e24b5d1a8ec78&quot;}" wire:effects="[]" wire:id="r55yIa4MK2h3bZFCzn9h">
    <button wire:click="logout" class="flex flex-row items-center p-3 gap-2 text-sm font-semibold text-error/80 hover:text-error bg-background-secondary hover:bg-background-secondary/80">
        Logout
    </button>
</div>                                        </div>
                                    </div>
                                </div>
                            </div>

                                                    </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</nav>
    <div class="w-full flex flex-grow">
                <div class=" flex flex-col flex-grow overflow-auto">
            <main class="container mt-24 mx-auto px-4 sm:px-6 md:px-8 lg:px-10">
                <div wire:snapshot="{&quot;data&quot;:{&quot;product&quot;:[null,{&quot;class&quot;:&quot;App\\Models\\Product&quot;,&quot;key&quot;:1,&quot;s&quot;:&quot;mdl&quot;}],&quot;category&quot;:[null,{&quot;class&quot;:&quot;App\\Models\\Category&quot;,&quot;key&quot;:2,&quot;s&quot;:&quot;mdl&quot;}],&quot;plan&quot;:[null,{&quot;class&quot;:&quot;App\\Models\\Plan&quot;,&quot;key&quot;:1,&quot;s&quot;:&quot;mdl&quot;}],&quot;plan_id&quot;:1,&quot;total&quot;:[{&quot;price&quot;:50000,&quot;currency&quot;:{&quot;code&quot;:&quot;IDR&quot;,&quot;prefix&quot;:&quot;Rp&quot;,&quot;suffix&quot;:null,&quot;format&quot;:&quot;1.000,00&quot;},&quot;setup_fee&quot;:0,&quot;has_setup_fee&quot;:false,&quot;is_free&quot;:null,&quot;dontShowUnavailablePrice&quot;:false,&quot;tax&quot;:0,&quot;setup_fee_tax&quot;:0,&quot;discount&quot;:0,&quot;original_price&quot;:50000,&quot;original_setup_fee&quot;:0,&quot;formatted&quot;:{&quot;price&quot;:&quot;Rp50.000,00&quot;,&quot;setup_fee&quot;:&quot;Rp0,00&quot;,&quot;tax&quot;:&quot;Rp0,00&quot;,&quot;setup_fee_tax&quot;:&quot;Rp0,00&quot;}},{&quot;s&quot;:&quot;price&quot;}],&quot;setup_fee&quot;:null,&quot;configOptions&quot;:[null,{&quot;keys&quot;:[],&quot;class&quot;:&quot;Illuminate\\Database\\Eloquent\\Collection&quot;,&quot;modelClass&quot;:null,&quot;s&quot;:&quot;elcln&quot;}],&quot;checkoutConfig&quot;:[{&quot;hostname&quot;:&quot;dev&quot;,&quot;template&quot;:&quot;9001&quot;,&quot;password&quot;:&quot;1sampai9&quot;,&quot;ssh_key&quot;:null,&quot;enable_proxy&quot;:null,&quot;ssl_enabled&quot;:null,&quot;domain&quot;:&quot;&quot;,&quot;enable_port_forward&quot;:null,&quot;port_forwards&quot;:null},{&quot;s&quot;:&quot;std&quot;}],&quot;cartProductKey&quot;:0},&quot;memo&quot;:{&quot;id&quot;:&quot;LkNXYXjPtZQgYsg8pMRl&quot;,&quot;name&quot;:&quot;products.checkout&quot;,&quot;path&quot;:&quot;products\/vps\/starter\/checkout&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;8b1c3c4bec9998e4ccdf9b7ef498521e5cec9cee9ffb678baa71819d60dbe505&quot;}" wire:effects="{&quot;url&quot;:{&quot;plan_id&quot;:{&quot;as&quot;:&quot;plan&quot;,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:true,&quot;except&quot;:null},&quot;configOptions&quot;:{&quot;as&quot;:&quot;options&quot;,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:true,&quot;except&quot;:null},&quot;checkoutConfig&quot;:{&quot;as&quot;:&quot;config&quot;,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:true,&quot;except&quot;:null},&quot;cartProductKey&quot;:{&quot;as&quot;:&quot;edit&quot;,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:false,&quot;except&quot;:null}},&quot;listeners&quot;:[&quot;currencyChanged&quot;]}" wire:id="LkNXYXjPtZQgYsg8pMRl" class="flex flex-col md:grid md:grid-cols-4 gap-6">
    <div class="flex flex-col gap-4 w-full col-span-3">
        <h1 class="text-3xl font-bold">Starter</h1>
        <div class="flex flex-row w-full gap-4">
            <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
            <div class="max-h-28 overflow-y-auto w-full">
                <article class="prose dark:prose-invert prose-sm">
                    
                </article>
            </div>
        </div>
        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
        <!--[if BLOCK]><![endif]-->                        <div >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->        
                
                            <fieldset class="flex flex-col relative mt-3 w-full ">
    <!--[if BLOCK]><![endif]-->        <legend>
            <label for="checkoutConfig.hostname"
                class="text-sm text-primary-100 absolute -translate-y-1/2 start-1 ml-1 bg-background-secondary px-2 rounded-md">
                Hostname
                <!--[if BLOCK]><![endif]-->                    <span class="text-red-500">*</span>
                <!--[if ENDBLOCK]><![endif]-->
            </label>
        </legend>
    <!--[if ENDBLOCK]><![endif]-->
    <input type="text" id="checkoutConfig.hostname" name="checkoutConfig.hostname"
        class="block w-full text-sm text-base bg-background-secondary border border-neutral rounded-md shadow-sm focus:outline-none transition-all duration-300 ease-in-out disabled:bg-background-secondary/50 disabled:cursor-not-allowed   px-2.5 py-2.5 "
        placeholder="app, db-srv, or example.com"
                wire:model.live="checkoutConfig.hostname" required />
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</fieldset>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
                                <div >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->            <fieldset class="flex flex-col relative mt-3 w-full ">
    <!--[if BLOCK]><![endif]-->    <legend>
        <label for="checkoutConfig.template"
            class="text-sm text-primary-100 absolute -translate-y-1/2 start-1 ml-1 bg-background-secondary rounded-md px-2">
            template
            <!--[if BLOCK]><![endif]-->            <span class="text-red-500">*</span>
            <!--[if ENDBLOCK]><![endif]-->
        </label>
    </legend>
    <!--[if ENDBLOCK]><![endif]-->

    <select id="checkoutConfig.template"  wire:model.live="checkoutConfig.template"
        class="block px-2.5 py-2.5 w-full text-sm text-primary-100 bg-background-secondary border-2 border-neutral
        rounded-md outline-none focus:outline-none focus:border-secondary transition-all duration-300 ease-in-out">
        <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <option value="5006">
                                Almalinux8-cloud
                            </option>
                                                    <option value="5007">
                                Alpine3.20-cloud
                            </option>
                                                    <option value="5005">
                                Centos9-cloud
                            </option>
                                                    <option value="5002">
                                Rocky9-cloud
                            </option>
                                                    <option value="5000">
                                Ubuntu22.04-cloud
                            </option>
                                                    <option value="5001">
                                Ubuntu24.04-cloud
                            </option>
                                                    <option value="5003">
                                bsd14.1-cloud
                            </option>
                                                    <option value="9001">
                                debian-12-cloud
                            </option>
                                                    <option value="5004">
                                debian12-cloud
                            </option>
                                                    <option value="9000">
                                ubuntu-2204-template
                            </option>
                        <!--[if ENDBLOCK]><![endif]-->
                    <!--[if ENDBLOCK]><![endif]-->
        <!--[if ENDBLOCK]><![endif]-->
    </select>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</fieldset>        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
                                <div >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->
                
                            <fieldset class="flex flex-col relative mt-3 w-full ">
    <!--[if BLOCK]><![endif]-->        <legend>
            <label for="checkoutConfig.password"
                class="text-sm text-primary-100 absolute -translate-y-1/2 start-1 ml-1 bg-background-secondary px-2 rounded-md">
                Root Password
                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
            </label>
        </legend>
    <!--[if ENDBLOCK]><![endif]-->
    <input type="password" id="checkoutConfig.password" name="checkoutConfig.password"
        class="block w-full text-sm text-base bg-background-secondary border border-neutral rounded-md shadow-sm focus:outline-none transition-all duration-300 ease-in-out disabled:bg-background-secondary/50 disabled:cursor-not-allowed   px-2.5 py-2.5 "
        placeholder=""
                wire:model.live="checkoutConfig.password"  />
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</fieldset>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->            <p class="text-xs text-primary-500">Leave empty for auto-generated password</p>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
                                <div >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->            <fieldset class="flex flex-col relative mt-3 w-full ">
    <!--[if BLOCK]><![endif]-->    <legend>
        <label for="checkoutConfig.ssh_key"
            class="text-sm text-primary-100 absolute -translate-y-1/2 start-1 ml-1 bg-primary-800 px-2 rounded-md">
            SSH Public Key (Optional)
            <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
        </label>
    </legend>
    <!--[if ENDBLOCK]><![endif]-->
    <textarea type="text" id="checkoutConfig.ssh_key" name="checkoutConfig.ssh_key"
        class="block w-full text-sm text-primary-100 bg-primary-800 border-2 border-neutral rounded-md outline-none focus:outline-none focus:border-secondary transition-all duration-300 ease-in-out disabled:bg-primary-700 disabled:cursor-not-allowed   px-2.5 py-2.5 "
        placeholder="ssh-rsa AAAAB3NzaC1yc2EAAAA..."  wire:model.live="checkoutConfig.ssh_key"
        ></textarea>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</fieldset>        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->            <p class="text-xs text-primary-500">SSH public key for key-based authentication</p>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
                                <div >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->            <div class="flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-neutral">
                <div class="flex flex-col">
                    <label class="text-sm font-medium text-base">Enable Reverse Proxy</label>
                    <!--[if BLOCK]><![endif]-->                        <p class="text-xs text-base/70">Enable reverse proxy management</p>
                    <!--[if ENDBLOCK]><![endif]-->
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" wire:model.live="checkoutConfig.enable_proxy" class="sr-only peer">
                    <div class="w-11 h-6 bg-neutral peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
            </div>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->            <p class="text-xs text-primary-500">Enable reverse proxy management</p>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
                                <div                  x-data="{ showField: window.Livewire.find('LkNXYXjPtZQgYsg8pMRl').entangle('checkoutConfig.enable_proxy') }"
                 x-show="showField"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->            <div class="flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-neutral">
                <div class="flex flex-col">
                    <label class="text-sm font-medium text-base">Enable SSL</label>
                    <!--[if BLOCK]><![endif]-->                        <p class="text-xs text-base/70">Enable SSL certificate (requires domain)</p>
                    <!--[if ENDBLOCK]><![endif]-->
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" wire:model.live="checkoutConfig.ssl_enabled" class="sr-only peer">
                    <div class="w-11 h-6 bg-neutral peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
            </div>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->            <p class="text-xs text-primary-500">Enable SSL certificate (requires domain)</p>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
                                <div                  x-data="{ showField: window.Livewire.find('LkNXYXjPtZQgYsg8pMRl').entangle('checkoutConfig.enable_proxy') }"
                 x-show="showField"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->        
                
                            <fieldset class="flex flex-col relative mt-3 w-full ">
    <!--[if BLOCK]><![endif]-->        <legend>
            <label for="checkoutConfig.domain"
                class="text-sm text-primary-100 absolute -translate-y-1/2 start-1 ml-1 bg-background-secondary px-2 rounded-md">
                Domain
                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
            </label>
        </legend>
    <!--[if ENDBLOCK]><![endif]-->
    <input type="text" id="checkoutConfig.domain" name="checkoutConfig.domain"
        class="block w-full text-sm text-base bg-background-secondary border border-neutral rounded-md shadow-sm focus:outline-none transition-all duration-300 ease-in-out disabled:bg-background-secondary/50 disabled:cursor-not-allowed   px-2.5 py-2.5 "
        placeholder="example.com"
                wire:model.live="checkoutConfig.domain"  />
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</fieldset>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->            <p class="text-xs text-primary-500">Domain name for proxy (required if proxy enabled)</p>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
                                <div >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->            <div class="flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-neutral">
                <div class="flex flex-col">
                    <label class="text-sm font-medium text-base">Enable Port Forwarding</label>
                    <!--[if BLOCK]><![endif]-->                        <p class="text-xs text-base/70">Enable custom port forwarding</p>
                    <!--[if ENDBLOCK]><![endif]-->
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" wire:model.live="checkoutConfig.enable_port_forward" class="sr-only peer">
                    <div class="w-11 h-6 bg-neutral peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
            </div>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->            <p class="text-xs text-primary-500">Enable custom port forwarding</p>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
                                <div                  x-data="{ showField: window.Livewire.find('LkNXYXjPtZQgYsg8pMRl').entangle('checkoutConfig.enable_port_forward') }"
                 x-show="showField"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 >
                <div class="flex flex-col gap-1">
    <!--[if BLOCK]><![endif]-->            <div class="flex flex-col gap-3 p-4 bg-background-secondary rounded-lg border border-neutral"
                 x-data="{
                     items: $wire.entangle('checkoutConfig.port_forwards') || [],
                     addItem() {
                         this.items.push('');
                         $wire.set('checkoutConfig.port_forwards', this.items);
                     },
                     removeItem(index) {
                         this.items.splice(index, 1);
                         $wire.set('checkoutConfig.port_forwards', this.items);
                     },
                     updateItem(index, value) {
                         this.items[index] = value;
                         $wire.set('checkoutConfig.port_forwards', this.items);
                     }
                 }">
                <div class="flex flex-col gap-1">
                    <label class="text-sm font-medium text-base">Port Forwarding</label>
                    <!--[if BLOCK]><![endif]-->                        <p class="text-xs text-base/70">Ports to forward (e.g., 22, 80, 443)</p>
                    <!--[if ENDBLOCK]><![endif]-->
                </div>

                <div class="flex flex-col gap-2">
                    <template x-for="(item, index) in items" :key="index">
                        <div class="flex gap-2 items-center">
                            <input type="number"
                                   :value="item"
                                   @input="updateItem(index, $event.target.value)"
                                   class="flex-1 px-3 py-2 bg-background border border-neutral rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   :placeholder="'80'">
                            <button type="button"
                                    @click="removeItem(index)"
                                    class="flex items-center justify-center w-8 h-8 bg-error text-white rounded-md hover:bg-error/80 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </template>

                    <button type="button"
                            @click="addItem()"
                            class="flex items-center justify-center gap-2 px-3 py-2 bg-primary text-white rounded-md hover:bg-primary/80 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Port
                    </button>
                </div>
            </div>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->            <p class="text-xs text-primary-500">Ports to forward (e.g., 22, 80, 443)</p>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
        <!--[if ENDBLOCK]><![endif]-->
    </div>
    <div class="flex flex-col gap-2 w-full col-span-1 bg-background-secondary p-3 rounded-md h-fit">
        <h2 class="text-2xl font-semibold  mb-2">
            Order Summary
        </h2>
        <div class="text- font-semibold flex justify-between">
            <h4>Total today:</h4> Rp50.000,00
        </div>
        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
        <!--[if BLOCK]><![endif]-->            <div>
                <button 
    class="flex items-center gap-2 justify-center bg-primary text-white text-sm font-semibold hover:bg-primary/80 py-2.5 lg:py-2 px-4.5 rounded-md w-full duration-300 cursor-pointer disabled:cursor-not-allowed disabled:opacity-50" wire:click="checkout" wire:loading.attr="disabled">
    <!--[if BLOCK]><![endif]-->        Checkout
    <!--[if ENDBLOCK]><![endif]-->
</button>
            </div>
        <!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
            </main>
            <div x-data>
    <template x-for="(notification, index) in $store.notifications.notifications" :key="notification.id">
        <div x-show="notification.show" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-300" x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-90" @click="$store.notifications.removeNotification(notification.id)"
            :class="notification.type === 'success' ? 'bg-secondary' : 'bg-red-500'"
            class="fixed text-white px-4 py-2 rounded shadow-md mb-4 z-50"
            :style="'top: ' + (20 + index * 60) + 'px;left: 50%; transform: translateX(-50%);'">
            <p x-text="notification.message"></p>
        </div>
    </template>
</div>
            <div class="py-8">
                <footer class="w-full px-4 py-4 lg:mt-72 mt-44">
    <div class="container mx-auto grid gap-4">
        <div class="flex flex-col gap-2 items-center">
            <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
            <div class="text-sm text-base/80">
                © 2025 Nex. | All rights reserved.
            </div>
            
            <a href="https://paymenter.org" target="_blank" 
                class="group mt-4 mb-6 flex items-center gap-2 text-base/50 hover:text-base">
                <svg class="size-4 text-current group-hover:text-[#4667FF]" width="150" height="205" viewBox="0 0 150 205" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_1_17)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 107V205H42.8571V139.638H100C133.333 139.638 150 123 150 89.7246V69.5L75 107V69.5L148.227 32.8863C143.133 10.9621 127.057 0 100 0H0V107ZM0 107V69.5L75 32V69.5L0 107Z"></path>
                    </g>
                    <defs>
                        <clipPath id="clip0_1_17">
                            <rect width="150" height="205"></rect>
                        </clipPath>
                    </defs>
                </svg>
                <p class="text-sm">Powered by Paymenter</p>
            </a>
        </div>
    </div>
</footer>            </div>
        </div>
    </div>
    
<!-- Livewire Scripts -->
<script src="/livewire/livewire.js?id=df3a17f2"   data-csrf="PUaNyWTHvRD0nvFCMEtkKA1dLpZBoX0dTpxcs3Zt" data-update-uri="/paymenter/update" data-navigate-once="true"></script>
</body>

</html>
