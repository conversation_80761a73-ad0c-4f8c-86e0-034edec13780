#!/usr/bin/env python3
"""
FastAPI Proxy Management System
Manages NGINX reverse proxy configurations and iptables port forwarding
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, validator
from typing import List, Optional, Dict, Any
import subprocess
import os
import json
import logging
from pathlib import Path
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app initialization
app = FastAPI(
    title="Proxmox Proxy Management API",
    description="API for managing NGINX reverse proxy and iptables port forwarding",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Configuration
NGINX_SITES_PATH = "/etc/nginx/sites-available"
NGINX_ENABLED_PATH = "/etc/nginx/sites-enabled"
IPTABLES_RULES_FILE = "/etc/iptables/rules.v4"
API_TOKEN = os.getenv("PROXY_API_TOKEN", "your-secure-token-here")

# Pydantic models
class ProxyConfig(BaseModel):
    domain: str
    target: str
    ssl: bool = False
    
    @validator('domain')
    def validate_domain(cls, v):
        if not re.match(r'^[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}$', v):
            raise ValueError('Invalid domain format')
        return v
    
    @validator('target')
    def validate_target(cls, v):
        if not re.match(r'^[0-9\.]+:[0-9]+$', v):
            raise ValueError('Target must be in format ip:port')
        return v

class PortForward(BaseModel):
    port: int
    target: str
    
    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v
    
    @validator('target')
    def validate_target(cls, v):
        if not re.match(r'^[0-9\.]+:[0-9]+$', v):
            raise ValueError('Target must be in format ip:port')
        return v

class ProxyResponse(BaseModel):
    status: str
    message: str
    domain: Optional[str] = None

class PortForwardResponse(BaseModel):
    status: str
    message: str
    port: Optional[int] = None
    target: Optional[str] = None

class PortForwardState(BaseModel):
    port: int
    dest: str

# Authentication dependency
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if credentials.credentials != API_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token"
        )
    return credentials.credentials

# Utility functions
def run_command(command: List[str], check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=check)
        logger.info(f"Command executed: {' '.join(command)}")
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {' '.join(command)}, Error: {e.stderr}")
        raise HTTPException(status_code=500, detail=f"Command execution failed: {e.stderr}")

def create_nginx_config(domain: str, target: str, ssl: bool = False) -> str:
    """Generate NGINX configuration for reverse proxy"""
    config = f"""server {{
    listen 80;
    server_name {domain};
    
    location / {{
        proxy_pass http://{target};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }}
}}"""

    if ssl:
        config += f"""

server {{
    listen 443 ssl http2;
    server_name {domain};
    
    ssl_certificate /etc/letsencrypt/live/{domain}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/{domain}/privkey.pem;
    
    location / {{
        proxy_pass http://{target};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }}
}}"""

    return config

def reload_nginx():
    """Reload NGINX configuration"""
    run_command(["nginx", "-t"])  # Test configuration first
    run_command(["systemctl", "reload", "nginx"])

def save_iptables():
    """Save current iptables rules"""
    run_command(["iptables-save"], check=False)
    run_command(["netfilter-persistent", "save"])

# API Routes
@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Proxmox Proxy Management API is running"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "nginx": "running" if os.path.exists("/var/run/nginx.pid") else "stopped",
        "iptables": "configured"
    }

@app.post("/api/proxy", response_model=ProxyResponse)
async def create_proxy(config: ProxyConfig, token: str = Depends(verify_token)):
    """Create or update reverse proxy configuration"""
    try:
        domain = config.domain
        target = config.target
        ssl = config.ssl
        
        # Create NGINX configuration
        nginx_config = create_nginx_config(domain, target, ssl)
        
        # Write configuration file
        config_path = Path(NGINX_SITES_PATH) / domain
        config_path.write_text(nginx_config)
        
        # Enable site
        enabled_path = Path(NGINX_ENABLED_PATH) / domain
        if enabled_path.exists():
            enabled_path.unlink()
        enabled_path.symlink_to(config_path)
        
        # Setup SSL if requested
        if ssl:
            try:
                run_command([
                    "certbot", "--nginx", "-d", domain, 
                    "--non-interactive", "--agree-tos", 
                    "--email", "admin@" + domain
                ])
            except Exception as e:
                logger.warning(f"SSL setup failed for {domain}: {e}")
        
        # Reload NGINX
        reload_nginx()
        
        logger.info(f"Proxy created for domain: {domain} -> {target}")
        return ProxyResponse(
            status="success",
            message=f"Proxy configuration created for {domain}",
            domain=domain
        )
        
    except Exception as e:
        logger.error(f"Failed to create proxy for {domain}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/proxy/{domain}")
async def delete_proxy(domain: str, token: str = Depends(verify_token)):
    """Delete reverse proxy configuration"""
    try:
        # Remove enabled site
        enabled_path = Path(NGINX_ENABLED_PATH) / domain
        if enabled_path.exists():
            enabled_path.unlink()
        
        # Remove configuration file
        config_path = Path(NGINX_SITES_PATH) / domain
        if config_path.exists():
            config_path.unlink()
        
        # Reload NGINX
        reload_nginx()
        
        logger.info(f"Proxy deleted for domain: {domain}")
        return ProxyResponse(
            status="success",
            message=f"Proxy configuration deleted for {domain}",
            domain=domain
        )
        
    except Exception as e:
        logger.error(f"Failed to delete proxy for {domain}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/proxy")
async def list_proxies(token: str = Depends(verify_token)):
    """List all configured proxies"""
    try:
        proxies = []
        enabled_path = Path(NGINX_ENABLED_PATH)
        
        if enabled_path.exists():
            for config_file in enabled_path.iterdir():
                if config_file.is_symlink():
                    proxies.append(config_file.name)
        
        return {"proxies": proxies}
        
    except Exception as e:
        logger.error(f"Failed to list proxies: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/port-forward", response_model=PortForwardResponse)
async def create_port_forward(pf: PortForward, token: str = Depends(verify_token)):
    """Create port forwarding rule"""
    try:
        port = pf.port
        target = pf.target
        target_ip, target_port = target.split(':')

        # Add iptables DNAT rule
        run_command([
            "iptables", "-t", "nat", "-A", "PREROUTING",
            "-p", "tcp", "--dport", str(port),
            "-j", "DNAT", "--to-destination", target
        ])

        # Add FORWARD rule
        run_command([
            "iptables", "-A", "FORWARD",
            "-p", "tcp", "-d", target_ip, "--dport", target_port,
            "-j", "ACCEPT"
        ])

        # Save iptables rules
        save_iptables()

        logger.info(f"Port forward created: {port} -> {target}")
        return PortForwardResponse(
            status="success",
            message=f"Port forwarding rule created: {port} -> {target}",
            port=port,
            target=target
        )

    except Exception as e:
        logger.error(f"Failed to create port forward {port} -> {target}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/port-forward/{port}")
async def delete_port_forward(port: int, token: str = Depends(verify_token)):
    """Delete port forwarding rule"""
    try:
        # Get current rules to find the exact rule to delete
        result = run_command(["iptables", "-t", "nat", "-L", "PREROUTING", "--line-numbers", "-n"])

        # Find and delete DNAT rule
        for line in result.stdout.split('\n'):
            if f"dpt:{port}" in line and "DNAT" in line:
                line_num = line.split()[0]
                if line_num.isdigit():
                    run_command([
                        "iptables", "-t", "nat", "-D", "PREROUTING", line_num
                    ])
                    break

        # Find and delete FORWARD rule
        result = run_command(["iptables", "-L", "FORWARD", "--line-numbers", "-n"])
        for line in result.stdout.split('\n'):
            if f"dpt:{port}" in line and "ACCEPT" in line:
                line_num = line.split()[0]
                if line_num.isdigit():
                    run_command([
                        "iptables", "-D", "FORWARD", line_num
                    ])
                    break

        # Save iptables rules
        save_iptables()

        logger.info(f"Port forward deleted: {port}")
        return PortForwardResponse(
            status="success",
            message=f"Port forwarding rule deleted for port {port}",
            port=port
        )

    except Exception as e:
        logger.error(f"Failed to delete port forward for port {port}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/port-forward")
async def list_port_forwards(token: str = Depends(verify_token)):
    """List all port forwarding rules"""
    try:
        forwards = []

        # Get DNAT rules
        result = run_command(["iptables", "-t", "nat", "-L", "PREROUTING", "-n"])

        for line in result.stdout.split('\n'):
            if "DNAT" in line and "dpt:" in line:
                # Parse iptables output to extract port and destination
                parts = line.split()
                port = None
                dest = None

                for part in parts:
                    if part.startswith("dpt:"):
                        port = int(part.split(":")[1])
                    elif part.startswith("to:"):
                        dest = part.split(":")[1]

                if port and dest:
                    forwards.append(PortForwardState(port=port, dest=dest))

        return {"forwards": [f.dict() for f in forwards]}

    except Exception as e:
        logger.error(f"Failed to list port forwards: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/port-forward/{port}")
async def get_port_forward(port: int, token: str = Depends(verify_token)):
    """Get specific port forwarding rule"""
    try:
        result = run_command(["iptables", "-t", "nat", "-L", "PREROUTING", "-n"])

        for line in result.stdout.split('\n'):
            if "DNAT" in line and f"dpt:{port}" in line:
                parts = line.split()
                dest = None

                for part in parts:
                    if part.startswith("to:"):
                        dest = part.split(":")[1]
                        break

                if dest:
                    return PortForwardState(port=port, dest=dest)

        raise HTTPException(status_code=404, detail=f"Port forward rule for port {port} not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get port forward for port {port}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
