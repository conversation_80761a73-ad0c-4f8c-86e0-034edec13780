version: '3.8'

services:
  proxy-api:
    build: .
    container_name: proxmox-proxy-api
    ports:
      - "8000:8000"
    environment:
      - PROXY_API_TOKEN=${PROXY_API_TOKEN:-your-secure-token-here}
    volumes:
      - /etc/nginx/sites-available:/etc/nginx/sites-available
      - /etc/nginx/sites-enabled:/etc/nginx/sites-enabled
      - /etc/letsencrypt:/etc/letsencrypt:ro
    network_mode: host
    privileged: true
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: proxmox-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /etc/nginx/sites-available:/etc/nginx/sites-available:ro
      - /etc/nginx/sites-enabled:/etc/nginx/sites-enabled:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    depends_on:
      - proxy-api
