#!/bin/bash

# Proxmox Proxy Setup Script
# Installs and configures NGINX, Certbot, iptables-persistent, and FastAPI proxy management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root"
fi

log "Starting Proxmox Proxy Setup..."

# Update system packages
log "Updating system packages..."
apt update
apt upgrade -y

# Install required packages
log "Installing required packages..."
apt install -y \
    nginx \
    python3-certbot-nginx \
    iptables-persistent \
    ufw \
    python3-pip \
    python3-venv \
    supervisor \
    curl \
    wget \
    git

# Configure UFW firewall
log "Configuring UFW firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# Allow SSH
ufw allow ssh

# Allow HTTP and HTTPS
ufw allow 'Nginx Full'

# Allow FastAPI port
ufw allow 8000

# Enable UFW
ufw --force enable

log "UFW firewall configured and enabled"

# Configure NGINX
log "Configuring NGINX..."

# Remove default site
rm -f /etc/nginx/sites-enabled/default

# Create custom nginx configuration
cat > /etc/nginx/nginx.conf << 'EOF'
user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections 768;
    # multi_accept on;
}

http {
    ##
    # Basic Settings
    ##
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    ##
    # SSL Settings
    ##
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;

    ##
    # Logging Settings
    ##
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    ##
    # Gzip Settings
    ##
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    ##
    # Virtual Host Configs
    ##
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF

# Test NGINX configuration
nginx -t

# Start and enable NGINX
systemctl enable nginx
systemctl start nginx

log "NGINX configured and started"

# Configure iptables for port forwarding
log "Configuring iptables..."

# Enable IP forwarding
echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf
sysctl -p

# Save current iptables rules
iptables-save > /etc/iptables/rules.v4
ip6tables-save > /etc/iptables/rules.v6

log "iptables configured"

# Setup Python environment for FastAPI
log "Setting up Python environment for FastAPI..."

# Create application directory
mkdir -p /opt/proxy-api
cd /opt/proxy-api

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install fastapi uvicorn[standard] pydantic python-multipart

# Create systemd service for FastAPI
cat > /etc/systemd/system/proxy-api.service << 'EOF'
[Unit]
Description=Proxy Management API
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/proxy-api
Environment=PATH=/opt/proxy-api/venv/bin
Environment=PROXY_API_TOKEN=your-secure-token-here
ExecStart=/opt/proxy-api/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# Create supervisor configuration as backup
cat > /etc/supervisor/conf.d/proxy-api.conf << 'EOF'
[program:proxy-api]
command=/opt/proxy-api/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
directory=/opt/proxy-api
user=root
autostart=true
autorestart=true
environment=PROXY_API_TOKEN="your-secure-token-here"
stdout_logfile=/var/log/proxy-api.log
stderr_logfile=/var/log/proxy-api-error.log
EOF

log "FastAPI environment configured"

# Generate secure API token
API_TOKEN=$(openssl rand -hex 32)
sed -i "s/your-secure-token-here/$API_TOKEN/g" /etc/systemd/system/proxy-api.service
sed -i "s/your-secure-token-here/$API_TOKEN/g" /etc/supervisor/conf.d/proxy-api.conf

# Create configuration file
cat > /opt/proxy-api/config.txt << EOF
API_TOKEN=$API_TOKEN
NGINX_SITES_PATH=/etc/nginx/sites-available
NGINX_ENABLED_PATH=/etc/nginx/sites-enabled
IPTABLES_RULES_FILE=/etc/iptables/rules.v4
EOF

log "API token generated and saved to /opt/proxy-api/config.txt"

# Enable and start services
systemctl daemon-reload
systemctl enable proxy-api
systemctl enable supervisor

log "Services configured"

# Create log rotation
cat > /etc/logrotate.d/proxy-api << 'EOF'
/var/log/proxy-api*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

# Final system configuration
log "Performing final system configuration..."

# Ensure directories exist
mkdir -p /etc/nginx/sites-available
mkdir -p /etc/nginx/sites-enabled

# Set proper permissions
chown -R www-data:www-data /var/log/nginx
chmod 755 /etc/nginx/sites-available
chmod 755 /etc/nginx/sites-enabled

# Create a simple health check endpoint
cat > /etc/nginx/sites-available/health << 'EOF'
server {
    listen 80 default_server;
    server_name _;
    
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    location / {
        return 404;
    }
}
EOF

ln -sf /etc/nginx/sites-available/health /etc/nginx/sites-enabled/health

# Test and reload NGINX
nginx -t
systemctl reload nginx

log "Setup completed successfully!"
log ""
log "=== IMPORTANT INFORMATION ==="
log "API Token: $API_TOKEN"
log "API URL: http://$(hostname -I | awk '{print $1}'):8000"
log "NGINX config directory: /etc/nginx/sites-available"
log "Logs: /var/log/proxy-api.log"
log ""
log "To start the FastAPI service:"
log "  systemctl start proxy-api"
log ""
log "To check service status:"
log "  systemctl status proxy-api"
log "  systemctl status nginx"
log ""
log "Health check: curl http://localhost/health"
log "API health: curl http://localhost:8000/health"

echo -e "${GREEN}Proxy setup completed successfully!${NC}"
