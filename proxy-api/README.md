# Proxmox Proxy Management API

FastAPI-based reverse proxy management system for Proxmox VE integration with Paymenter.

## Features

- **Reverse Proxy Management**: Create and manage NGINX reverse proxy configurations
- **SSL Certificate Management**: Automatic SSL certificate generation with Let's Encrypt
- **Port Forwarding**: iptables-based port forwarding rules
- **RESTful API**: Clean REST API for integration with Paymenter
- **Security**: Token-based authentication
- **Docker Support**: Containerized deployment option

## Installation

### Method 1: Automated Setup Script

Run the automated setup script as root:

```bash
sudo bash proxy-setup.sh
```

This script will:
- Install NGINX, Certbot, iptables-persistent
- Configure UFW firewall
- Set up Python virtual environment
- Install FastAPI dependencies
- Create systemd service
- Generate secure API token

### Method 2: Docker Deployment

1. Clone the repository:
```bash
git clone <repository-url>
cd proxy-api
```

2. Set environment variables:
```bash
export PROXY_API_TOKEN="your-secure-token-here"
```

3. Deploy with Docker Compose:
```bash
docker-compose up -d
```

### Method 3: Manual Installation

1. Install dependencies:
```bash
apt update
apt install -y nginx python3-certbot-nginx iptables-persistent python3-pip python3-venv
```

2. Create virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

3. Run the application:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

## API Endpoints

### Authentication

All endpoints require Bearer token authentication:
```
Authorization: Bearer your-api-token
```

### Proxy Management

#### Create Reverse Proxy
```http
POST /api/proxy
Content-Type: application/json

{
    "domain": "example.com",
    "target": "*************:80",
    "ssl": true
}
```

#### List Proxies
```http
GET /api/proxy
```

#### Delete Proxy
```http
DELETE /api/proxy/example.com
```

### Port Forwarding

#### Create Port Forward
```http
POST /api/port-forward
Content-Type: application/json

{
    "port": 22,
    "target": "*************:22"
}
```

#### List Port Forwards
```http
GET /api/port-forward
```

#### Get Specific Port Forward
```http
GET /api/port-forward/22
```

#### Delete Port Forward
```http
DELETE /api/port-forward/22
```

### Health Check

```http
GET /health
```

## Configuration

### Environment Variables

- `PROXY_API_TOKEN`: API authentication token
- `NGINX_SITES_PATH`: Path to NGINX sites-available directory (default: `/etc/nginx/sites-available`)
- `NGINX_ENABLED_PATH`: Path to NGINX sites-enabled directory (default: `/etc/nginx/sites-enabled`)

### Security

1. **Change Default Token**: Always change the default API token
2. **Firewall**: Configure UFW or iptables to restrict access
3. **HTTPS**: Use HTTPS in production environments
4. **Network**: Run on private network when possible

## Integration with Paymenter

Configure the Proxmox extension in Paymenter with:

1. **Proxy API URL**: `http://your-server:8000`
2. **Proxy Token**: Your generated API token

The extension will automatically:
- Create reverse proxy configurations for new VMs
- Set up SSL certificates when requested
- Configure port forwarding rules
- Clean up configurations when VMs are terminated

## Troubleshooting

### Check Service Status
```bash
systemctl status proxy-api
systemctl status nginx
```

### View Logs
```bash
journalctl -u proxy-api -f
tail -f /var/log/nginx/error.log
```

### Test API
```bash
curl -H "Authorization: Bearer your-token" http://localhost:8000/health
```

### NGINX Configuration Test
```bash
nginx -t
```

### Reload NGINX
```bash
systemctl reload nginx
```

## Development

### Running in Development Mode
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### API Documentation
Visit `http://localhost:8000/docs` for interactive API documentation.

## License

This project is part of the Paymenter Proxmox extension.
