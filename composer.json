{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2 || ^8.3", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-zip": "*", "andreiio/blade-remix-icon": "^3.6.0", "barryvdh/laravel-dompdf": "^3.0", "endroid/qr-code": "^5.0", "filament/filament": "^3.2", "flowframe/laravel-trend": "^0.4.0", "laravel/framework": "^12.0", "laravel/passport": "^12.0", "laravel/socialite": "^5.14", "laravel/tinker": "^2.9", "qirolab/laravel-themer": "^2.2.1", "robthree/twofactorauth": "^3.0", "socialiteproviders/discord": "^4.2"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "livewire/livewire": "^3.4", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Paymenter\\Extensions\\": "extensions/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#c4b5fd,#fdba74\" \"php artisan queue:listen --tries=1\" \"npm run dev\" --names=queue,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}