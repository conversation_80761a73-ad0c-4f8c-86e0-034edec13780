@import 'tailwindcss';

@plugin '@tailwindcss/forms' {
  strategy: class;
}

@plugin '@tailwindcss/typography';

@custom-variant dark (&:where(.dark, .dark *));

@source "../views";
@source "../../../extensions/**/*.blade.php";

@theme {
  /* Font Family */
  --font-sans: "Nunito", ui-sans-serif, system-ui, sans-serif;
}

@theme inline {
  /* Branding Colors */
  --color-primary: hsl(var(--color-primary));
  --color-secondary: hsl(var(--color-secondary));
  
  /* Neutral Colors */
  --color-neutral: hsl(var(--color-neutral));
  
  /* Text Colors */
  --color-base: hsl(var(--color-base));
  --color-muted: hsl(var(--color-muted));
  --color-inverted: hsl(var(--color-inverted));
  
  /* State Colors */
  --color-success: hsl(var(--color-success));
  --color-error: hsl(var(--color-error));
  --color-warning: hsl(var(--color-warning));
  --color-inactive: hsl(var(--color-inactive));
  --color-info: hsl(var(--color-info));
  
  /* Background Colors */
  --color-background: hsl(var(--color-background));
  --color-background-secondary: hsl(var(--color-background-secondary));
}

/*
The default border color has changed to `currentColor` in Tailwind CSS v4,
so we've added these compatibility styles to make sure everything still
looks the same as it did with Tailwind CSS v3.

If we ever want to remove these styles, we need to add an explicit border
color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@utility text-nowrap {
  text-wrap: nowrap;
}

@utility text-wrap {
  text-wrap: wrap;
}

@import './easymde.css'
